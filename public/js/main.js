$(function() {
    $('.pleaseConfirm').click(function(e) {
        e.preventDefault();
        let question = $(e.target).data('confirmation');
        if (window.confirm(question)) {
            location.href = this.href;
        }
    });


});

$.extend($.fn.dataTableExt.oStdClasses, {
    "sFilterInput": "form-control pl-2",
});

function initializeTextEditor(selector, image_upload_route, token, is_dark = false)
{

    let customCSS = '';

    if(window.theme === 'default-dark') {
        customCSS = `
        [data-mce-selected=inline-boundary] {
          background-color: #2c4057 !important;
        }
    `;
    }

    let setup = {
        selector: selector,
        content_style: customCSS,
        relative_urls : false,
        elementpath: false,
        remove_script_host : false,
        convert_urls : true,
        height: 300,
        contextmenu: false,
        images_upload_handler: function (blobInfo, success, failure) {
            let xhr, formData;
            xhr = new XMLHttpRequest();
            xhr.withCredentials = false;
            xhr.open('POST', image_upload_route);
            xhr.setRequestHeader("X-CSRF-Token", token);
            xhr.onload = function() {
                let json;
                if (xhr.status != 200) {
                    failure('HTTP Error: ' + xhr.status);
                    return;
                }
                json = JSON.parse(xhr.responseText);

                if (!json || typeof json.location != 'string') {
                    failure('Invalid JSON: ' + xhr.responseText);
                    return;
                }
                success(json.location);
            };
            formData = new FormData();
            formData.append('file', blobInfo.blob(), blobInfo.filename());
            xhr.send(formData);
        },
        language: document.documentElement.lang,
        menubar: false,
        paste_text_sticky: true,
        paste_text_sticky_default: true,
        plugins: 'advlist link image lists code codesample textcolor hr paste table autolink',
        toolbar: 'formatselect | fontsizeselect | bold italic underline strikethrough | alignleft aligncenter alignright alignfull | forecolor backcolor | removeformat | link image hr codesample | numlist bullist | outdent indent | code | table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol',
    };

    if(is_dark){
        setup.skin = 'oxide-dark';
        setup.content_css = 'dark';
    }

    tinymce.init(setup);
}

function isMobile()
{
    return screen.width < 600;
}

function addCopyButtonToPreCode() {
    // add copy button to code blocks
    $('pre code').each(function() {
        let $code = $(this);
        let $pre = $code.parent();
        let $button = $('<button class="btn btn-default btn-xs copy-btn" title="Copy to clipboard"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16"><path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/> </svg></button>').css({
            position: 'absolute',
            top: '15px',
            right: '7px',
            zIndex: 1000,
            cursor: 'pointer'
        });

        $button.on('click', function() {
            let text = $code.text();
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).catch(err => {
                    console.error('Failed to copy text: ', err);
                });
            } else {
                // Fallback for browsers that don't support clipboard API
                let textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                }
                document.body.removeChild(textArea);
            }
        });

        // Create a wrapper div
        let $wrapper = $('<div>').css({
            position: 'relative',
            overflow: 'auto'
        });

        // Wrap the existing code in the new wrapper
        $pre.wrap($wrapper);

        // Append the button to the wrapper instead of the pre
        $pre.parent().append($button);

        // Remove the relative positioning from pre
        $pre.css('position', '');
    });
}

addCopyButtonToPreCode();
