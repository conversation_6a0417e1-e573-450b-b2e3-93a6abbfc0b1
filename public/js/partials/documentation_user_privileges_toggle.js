$(document).ready(function() {
    // toggle select all user access privileges
    $('#select-all-users-access').on('click', function(e){
        $( ".user-access-checkbox" ).prop('checked', $(this).is(':checked'));

        // if unchecked, uncheck also all user access privileges
        if(!$(this).is(':checked')){
            $('#select-all-users-editing').prop('checked', false);
            $( ".user-editing-checkbox" ).prop('checked', false);
        }
    })

    // toggle select all user editing privileges
    $('#select-all-users-editing').on('click', function(e){
        $( ".user-editing-checkbox" ).prop('checked', $(this).is(':checked'));

        // if checked, check also all user access privileges
        if($(this).is(':checked')){
            $('#select-all-users-access').prop('checked', true);
            $( ".user-access-checkbox" ).prop('checked', true);
        }
    })

    // toggle single user access privileges
    $( ".user-access-checkbox" ).on('click', function (e){
        let checked = $(this).is(':checked');

        // if access privilege is toggled off, remove edit privilege for that user
        if(!checked){
            $(this).closest('tr').find('.user-editing-checkbox').prop('checked', false);
        }

        // if all users checked, check the select-all checkbox
        if($( ".user-access-checkbox" ).length == $( ".user-access-checkbox:checked" ).length) {
            $('#select-all-users-access').prop('checked', true);
        }
        else{
            $('#select-all-users-access').prop('checked', false);
            $('#select-all-users-editing').prop('checked', false);
        }
    })

    // toggle single user editing privileges
    $( ".user-editing-checkbox" ).on('click', function (e){

        let checked = $(this).is(':checked');

        // if editing privilege is toggled on, add access privilege for that user
        if(checked){
            $(this).closest('tr').find('.user-access-checkbox').prop('checked', true);
        }

        // if all users checked, check the select-all checkbox
        if($( ".user-editing-checkbox" ).length == $( ".user-editing-checkbox:checked" ).length) {
            $('#select-all-users-access').prop('checked', true);
            $('#select-all-users-editing').prop('checked', true);
        }
        else{
            $('#select-all-users-editing').prop('checked', false);
        }
    })
});
