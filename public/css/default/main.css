html{
    overflow-y:scroll;
}

.card-header {
    font-size: 1.5em;
}

.fixed-height-body {
    min-height: 650px;
}

@media (min-width: 768px) {
    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: 1rem;
        padding-left: 1rem;
    }


    .navbar-nav .dropdown:hover > .dropdown-menu {
        display: block;
    }

    .navbar-nav .dropdown-menu {
        margin-top: 0;
    }


    .navbar .divider-vertical {
        height: 40px;
        margin: 0 10px;
        border-right: 1px solid #ffffff;
        border-left: 1px solid #f2f2f2;
    }
}

.dt-link, .dt-link:hover {
    color: black;

}

.topic-description {
    border: 1px solid #e0dfdf;
    padding: 1em;
    margin: 1em 0 2em;
    background: #f7f7f7;
}

.progress {
    margin-top: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
}

.select2-container--default .select2-selection--single, .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px;
}

#documentationTopicFilter .select2-selection__rendered{
    margin-top: 3px!important;
}


.btn-group-xs > .btn, .btn-xs {
    padding: .25rem .4rem;
    font-size: .875rem;
    line-height: .5;
    border-radius: .2rem;
}

.checkmark {
    color: green;
}

.btn-default {
    background: white;
    color: black;
    border: 1px solid silver;
}

.select2-container--default .select2-selection--single, .select2-container--default .select2-selection--multiple {
    border: 1px solid #ced4da;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
    background-color: #f8f9fa;
}

#pills-tab .nav-link {
    color: #1f1e1e;
}

#pills-tab .nav-link.active {
    background-color: #f8f9fa;
    border: 1px solid #e8e8e8;
}

.card-header .select2 {
    font-size: 15px;
}

.btn-primary.dropdown-toggle {
    background-color: #3490dc !important;
}

.navbar-nav .dropdown-item:active {
    background: white;
    color: black;
}

.comments-table {
    border-collapse: separate;
    margin-top: -20px;
    border-spacing: 0 20px;
    border: 0;
}

.comments-table tr, .comments-table td {
    border-radius: 5px;
    padding: 1.2rem;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #717171!important;
}

.error {
    color: red;
}

span.select2-selection.select2-selection--single {
    outline: none;
}

#topic-nav a {
    color: #3a3a3a;
}

#topic-nav a.disabled {
    color: #b6b6b6;
}

.alert-warning hr {
    border-top-color: #e0ddba;
}

.search-comments-box {
    background: #f5f5f5;
    padding: 15px 0;
    margin: 0;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.send-notification {
    cursor: pointer;
}

.alert-warning {
    color: #857b26;
    background-color: #fffbdb;
    border-color: #ece9c7;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #fafafa;
}

a.btn-default:hover {
    color: black;
}

.select2-selection--multiple .select2-search__field {
    width: 100% !important;
    padding-left: 6px!important;
}

.jstree-default .jstree-clicked {
    background: #FFFFFF!important;
    border-radius: 0!important;
    box-shadow: none!important;
}

.dataTables_filter input{
    margin-left: 0!important;
    width: 290px;
}

.dataTables_length{
    position: relative;
    top: 10px;
    padding-bottom: 10px;
}

.card{
    -webkit-box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
    box-shadow: 0 .15rem 1.75rem 0 rgba(58,59,69,.15)!important;
}

.comment-content p{
    word-break: break-word!important;
}

.comment-content p{
    word-break: break-word;
    white-space: normal!important;
}

.comment-content p > span{
    word-break: break-word;
    white-space: normal!important;
}

.note-modal .modal-header {
    display: block;
}

pre{
    background: white;
    padding: 10px;
    overflow: auto;
    border: 1px dotted gray;
    margin: 5px;
/*    -webkit-user-select: all;  !* Chrome 49+ *!
    -moz-user-select: all;     !* Firefox 43+ *!
    -ms-user-select: all;      !* No support yet *!
    user-select: all;*/
}

.user-avatar{
    height: 20px;
    width: 20px;
    margin-right: 5px;
}

@media (max-width: 768px) {
    pre{
        max-width:220px;
        overflow: auto;
    }

    .user-avatar{
        display: none;
    }

    .category-crud-button{
        margin-top: 10px;
        width: 100%;
        display: block;
    }

    .dt-buttons{
        text-align: center;
    }

}

.dt-buttons{
    padding-bottom: 10px;
}

.bordered-div{
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.comment-content.collapsed {
    max-height: 150px;
    overflow: hidden;
}

.expand-comment{
    cursor: pointer;
    color: #3490dc!important;
}

.expand-comment:hover{
    text-decoration: underline!important;
}

.documentation-body {
    background: #fdfdfd;
    border: 1px solid #eaeaea;
    border-radius: 0;
}

table.dataTable.no-footer {
    margin-bottom: 5px;
}
