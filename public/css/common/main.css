#undo-filter-comments{
    font-size: 0.8em;
}

.comments-table{
    table-layout: fixed;
}

/* jstree search highlight */
.jstree-search {
    font-style: normal!important;
    font-weight: normal!important;
    background: rgba(101, 187, 220, 0.27);
}

.delete-upload, .remove-comment-reference{
    margin-bottom: 2px;
    padding-bottom: 6px;
}

.pagination{
    overflow-x: auto;
}

.tox-statusbar {
    border:none!important;
}

.comment-link, .comment-link:hover, .comment-link:visited{
    color: inherit;
}

.category-badge {
    cursor: pointer;
}

#loader {
    display: flex;
    justify-content: center; /* Center horizontally */
    align-items: center; /* Center vertically */
    height: 100%; /* You might need to adjust this */
}

.documentation-body {
    border-radius: 0;
}

.task-count-badge{
    height: 17px;
    line-height: 1.3em;
    padding-left: 5px;
    position: relative;
    top: -1px;
    font-size: 70%;
}

.task-count-menu-badge {
    padding-left: 6px;
    position: relative;
    top: -1px;
    font-size: 12px;
}


@media (max-width: 768px) {
    .copy-btn {
        display: none;
    }
}

/* Styles for mobile devices */
@media screen and (max-width: 767px) {
    ul, ol {
        padding-left: 16px;
    }

    /* Reset padding for nested lists beyond the second level */
    ul ul ul,
    ul ol ol,
    ol ul ul,
    ol ol ol {
        padding-left: 0;
    }

    .topic-list-categories, .documentation-list-categories {
        display: none;
    }

}

.topic-list-categories, .documentation-list-categories {
    font-size: 16px;
}

.dropdown:hover .dropdown-menu {
    display: block;
}
