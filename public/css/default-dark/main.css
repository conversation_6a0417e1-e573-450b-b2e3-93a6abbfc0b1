html{
    overflow-y:scroll;
}

.card-header {
    font-size: 1.5em;
}

.fixed-height-body {
    min-height: 650px;
}

@media (min-width: 768px) {
    .navbar-expand-md .navbar-nav .nav-link {
        padding-right: 1rem;
        padding-left: 1rem;
    }


    .navbar-nav .dropdown:hover > .dropdown-menu {
        display: block;
    }

    .navbar-nav .dropdown-menu {
        margin-top: 0;
    }


    .navbar .divider-vertical {
        height: 40px;
        margin: 0 10px;
        border-right: 1px solid #353535;
        border-left: 1px solid #353535;
    }
}

.nav-link {
    padding: 0.5rem 1rem;
}

.dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_processing, .dataTables_wrapper .dataTables_paginate {
    color: #ffffff;
}

.navbar {
    padding: 0.5rem 0.5rem;
}

.form-control {
    background-color: #444;
    border-color: #4b4b4b;
}

.form-control:focus {
    color: #ffeded;
    background-color: #444;
    border-color: #000000;
}

.btn-primary {
    border:none;
}

.dt-link, .dt-link:hover{
    color:#fff;
}

.badge-success {
    color: #fff;
    background-color: #0d751f;
}

.topic-description {
    border: 1px solid rgba(224, 223, 223, 0);
    padding: 1em;
    margin: 1em 0 2em;
    background: #024d6073;
    color: #b6b6b6;
}

.progress {
    margin-top: 20px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
}

.select2-container--default .select2-selection--single, .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px;
}

#documentationTopicFilter .select2-selection__rendered{
    margin-top: 3px!important;
}


.btn-group-xs > .btn, .btn-xs {
    padding: .25rem .4rem;
    font-size: .875rem;
    line-height: .5;
    border-radius: .2rem;
}

.checkmark {
    color: green;
}

.select2-container--default .select2-selection--single, .select2-container--default .select2-selection--multiple {
    border: 1px solid #ced4da;
}

.card-header .select2 {
    font-size: 15px;
}

.btn-primary.dropdown-toggle {
    background-color: #375a7f !important;
}

.form-control {
    color: #FFF;
}
pre{
    color: #FFF!important;
    background: #3e3e3e!important;
}
.navbar-nav .dropdown-item:active {
    background: white;
    color: black;
}

.comments-table {
    border-collapse: separate;
    margin-top: -20px;
    border-spacing: 0 20px;
    border: 0;
}

.comments-table tr, .comments-table td {
    border-radius: 5px;
    padding: 1.2rem;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #717171!important;
}

.error {
    color: red;
}

span.select2-selection.select2-selection--single {
    outline: none;
}

#topic-nav a {
    color: #FFF;
}

#topic-nav a.disabled {
    color: #b6b6b6;
}

.alert-warning hr {
    border-top-color: #384549;
}

.search-comments-box {
    background: #f5f5f5;
    padding: 15px 0;
    margin: 0;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.send-notification {
    cursor: pointer;
}


.alert-danger {
    background-color: #d84d3d38;
    color: white;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.04);
}

.select2-selection--multiple .select2-search__field {
    width: 100% !important;
    padding-left: 6px!important;
}

.dataTables_filter input{
    margin-left: 0!important;
    width: 290px;
}

.dataTables_length{
    position: relative;
    top: 10px;
    padding-bottom: 10px;
}

.comment-content p{
    word-break: break-word!important;
}

.comment-content p{
    word-break: break-word;
    white-space: normal!important;
}

.comment-content p > span{
    word-break: break-word;
    white-space: normal!important;
}

.note-modal .modal-header {
    display: block;
}

pre{
    background: white;
    padding: 10px;
    overflow: auto;
    border: 1px dotted gray;
    margin: 5px;
    /*-webkit-user-select: all;  !* Chrome 49+ *!
    -moz-user-select: all;     !* Firefox 43+ *!
    -ms-user-select: all;      !* No support yet *!
    user-select: all;*/
}

table.dataTable tbody tr {
    background-color: #383838;
}

.user-avatar{
    height: 20px;
    width: 20px;
    margin-right: 5px;
}

@media (max-width: 768px) {
    pre{
        max-width:220px;
        overflow: auto;
    }

    .user-avatar{
        display: none;
    }

    .category-crud-button{
        margin-top: 10px;
        width: 100%;
        display: block;
    }

    .dt-buttons{
        text-align: center;
    }

}

.dt-buttons{
    padding-bottom: 10px;
}

.dt-button{
    background: #3a3a3a;
    padding: 10px;
    border: none;
    color: #FFF;
}

.alert-success {
    background-color: #375a7f;
}

.alert-warning{
    color: #ffffff;
    background-color: #21434c73;
}

.documentation-body {
    background: #3f4040
}

.select2-container--default .select2-selection--single {
    background:#444444;
    border: 1px solid #4b4b4b;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #FFF;
}

.select2-container--default .select2-results__option[aria-selected=true], .select2-results__option {
    background:#444444;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background: #28415b;
}

.select2-dropdown{
    border: 1px solid #000;
}

.select2-container--default .select2-selection--multiple {
    background:#444444;
    border: 1px solid #4b4b4b;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background: #303030;
    border: 1px solid #4b4b4b;
}

.select2-search__field{
    color: #FFF;
}

.select2-search__field{
    background:#444444;
}

.select2-search.select2-search--dropdown{
    background:#444444;
}

.note-editor.note-airframe, .note-editor.note-frame {
    border: 1px solid #1d1d1d;
}

.note-editor.note-airframe .note-editing-area .note-editable, .note-editor.note-frame .note-editing-area .note-editable {
    background: #444444;
    color: #fff;
}

.note-editor.note-airframe .note-statusbar, .note-editor.note-frame .note-statusbar {
    border-top: 1px solid #222224;
}

.note-resizebar{
    background: #303030;
}

#uncategorized-topics-table > tbody > tr.selected{
    background: #365a7f;
}

#uncategorized-topics-table_length > label > select{
    color: #000;
    background: #FFF;
}

#uncategorized-documentation-table > tbody > tr.selected{
    background: #365a7f;
}

#uncategorized-documentation-table_length > label > select{
    color: #000;
    background: #FFF;
}

.jstree-default-dark .jstree-hovered, .jstree-default-dark .jstree-clicked{
    background: #365a7f !important;
}

a.paginate_button{
    color: white !important;
    border: 1px solid #111;
    background-color: #585858!important;
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #585858), color-stop(100%, #111))!important;
    background: -webkit-linear-gradient(top, #585858 0%, #111 100%)!important;
    background: -moz-linear-gradient(top, #585858 0%, #111 100%)!important;
    background: -ms-linear-gradient(top, #585858 0%, #111 100%)!important;
    background: -o-linear-gradient(top, #585858 0%, #111 100%)!important;
    background: linear-gradient(to bottom, #585858 0%, #111 100%)!important;
}

a.paginate_button.current{
    border: 1px solid #6e6e6e !important;
}

.card-header{
    background-color: #3f4040;
}

.nav-tabs .nav-link.active{
    background-color: #303030;
}

table.dataTable tbody td.select-checkbox:before, table.dataTable tbody th.select-checkbox:before {
    border: 1px solid #fff;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #fff;
}

.btn-default{
    background: #484848;
}

.jstree-default-dark {
    background: #303030!important;
}

.bordered-div{
    padding: 20px;
    border: 1px solid #444444;
    border-radius: 5px;
}

.select2-container--open .select2-dropdown--below {
    background: #303030;
}

.comment-content.collapsed {
    max-height: 150px;
    overflow: hidden;
}

.expand-comment{
    cursor: pointer;
    color: #00bc8c!important;
}

.expand-comment:hover{
    text-decoration: underline!important;
}

.card {
    border-color: #434343;
}
