{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ankane/pgvector": "^0.1.3", "doctrine/dbal": "^2.9", "elasticsearch/elasticsearch": "^8.11", "fideloper/proxy": "^4.0", "guzzlehttp/guzzle": "^7.8", "jenssegers/agent": "^2.6", "laravel/framework": "^9.0", "laravel/telescope": "^4.0", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "laravelcollective/html": "^6.0", "laravolt/avatar": "^4.0", "orhanerday/open-ai": "^5.0", "predis/predis": "^1.1", "spatie/laravel-backup": "^8.0", "yajra/laravel-datatables": "^9.15.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.5", "beyondcode/laravel-dump-server": "^1.0", "filp/whoops": "^2.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}