<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddIndexToTopicCommentEmbedding extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('CREATE INDEX embedding_index ON topic_comments USING hnsw (embedding vector_l2_ops)');
    }

    public function down()
    {
        DB::statement('DROP INDEX embedding_index');
    }
}
