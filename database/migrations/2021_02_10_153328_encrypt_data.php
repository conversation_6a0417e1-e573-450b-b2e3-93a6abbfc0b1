<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Crypt;

class EncryptData extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $documentation = \App\Documentation::withTrashed()->get();

        foreach($documentation as $_documentation){
            $_documentation->timestamps = false;
            $_documentation->content = Crypt::encrypt($_documentation->content);
            $_documentation->save();
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
