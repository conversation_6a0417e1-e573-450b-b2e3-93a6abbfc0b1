<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddVideoFieldsToUploadsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('uploads', function (Blueprint $table) {
            $table->string('mime_type')->nullable()->after('path');
            $table->bigInteger('file_size')->nullable()->after('mime_type');
            $table->integer('duration')->nullable()->after('file_size')->comment('Video duration in seconds');
            $table->string('thumbnail_path')->nullable()->after('duration');
            $table->json('video_metadata')->nullable()->after('thumbnail_path')->comment('Additional video metadata like resolution, codec, etc.');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('uploads', function (Blueprint $table) {
            $table->dropColumn(['mime_type', 'file_size', 'duration', 'thumbnail_path', 'video_metadata']);
        });
    }
}
