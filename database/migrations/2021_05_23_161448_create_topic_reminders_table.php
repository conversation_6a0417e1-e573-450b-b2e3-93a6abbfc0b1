<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTopicRemindersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // drop note column
        Schema::table('topics', function(Blueprint $table){
            $table->dropColumn('note');
        });

        Schema::create('topic_reminders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('message');
            $table->unsignedInteger('status_id')->default(1)->index();
            $table->unsignedInteger('frequency_id')->default(1)->index();
            $table->date('date')->nullable();
            $table->unsignedInteger('day_of_the_week')->nullable();
            $table->unsignedInteger('day_of_the_month')->nullable();
            $table->string('time_hours');
            $table->string('time_minutes');
            $table->bigInteger('topic_id')->index();
            $table->json('recipients');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('topic_reminders');
    }
}
