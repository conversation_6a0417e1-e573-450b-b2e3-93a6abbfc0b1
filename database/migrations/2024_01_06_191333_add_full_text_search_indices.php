<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Full-text index for topic_comments.comment
        DB::statement('CREATE INDEX topic_comments_comment_fulltext ON topic_comments USING gin (to_tsvector(\'simple\', comment));');

        // Full-text index for topics.title
        DB::statement('CREATE INDEX topics_title_fulltext ON topics USING gin (to_tsvector(\'simple\', title));');

        // Full-text index for categories.title
        DB::statement('CREATE INDEX categories_title_fulltext ON categories USING gin (to_tsvector(\'simple\', title));');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('DROP INDEX topic_comments_comment_fulltext;');
        DB::statement('DROP INDEX topics_title_fulltext;');
        DB::statement('DROP INDEX categories_title_fulltext;');
    }
};
