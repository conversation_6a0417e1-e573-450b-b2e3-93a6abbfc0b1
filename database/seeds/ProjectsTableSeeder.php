<?php

use Illuminate\Database\Seeder;
use \Illuminate\Support\Facades\DB as DB;

class ProjectsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $num_of_records = 10;

        $faker = \Faker\Factory::create();
        $userIDs = DB::table('users')->pluck('id')->toArray();

        foreach (range(1,$num_of_records) as $index) {
            DB::table('projects')->insert([
                'user_id' => $faker->randomElement($userIDs),
                'title' => $faker->sentence,
                'description' => $faker->text,
                'status_id' => $faker->randomElement(\App\Project::$statuses),
                'created_at' => \Carbon\Carbon::now()->toDateTimeString()

            ]);
        }
    }
}
