<?php

use Illuminate\Database\Seeder;

class TopicUsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = \Faker\Factory::create();
        $userIDs = \Illuminate\Support\Facades\DB::table('users')->pluck('id')->toArray();
        $topicIDs = \Illuminate\Support\Facades\DB::table('topics')->pluck('id')->toArray();

        foreach (range(0,count($topicIDs)-1) as $index) {
            \Illuminate\Support\Facades\DB::table('topic_users')->insert([
                'user_id' => $faker->randomElement($userIDs),
                'topic_id' => $topicIDs[$index],
                'type_id' => $faker->randomElement(\App\TopicUser::$types),
                'created_at' => \Carbon\Carbon::now()->toDateTimeString()
            ]);
        }
    }
}
