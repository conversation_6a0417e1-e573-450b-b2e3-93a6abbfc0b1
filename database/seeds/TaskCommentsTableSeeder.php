<?php

use Illuminate\Database\Seeder;

class TopicCommentsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = \Faker\Factory::create();
        $userIDs = \Illuminate\Support\Facades\DB::table('users')->pluck('id')->toArray();
        $topicIDs = \Illuminate\Support\Facades\DB::table('topics')->pluck('id')->toArray();

        foreach (range(0,count($topicIDs)-1) as $index) {

            $num_of_comments = rand(0,5);

            for($i = 0; $i < $num_of_comments; $i++){

                \Illuminate\Support\Facades\DB::table('topic_comments')->insert([
                    'user_id' => $faker->randomElement($userIDs),
                    'topic_id' => $topicIDs[$index],
                    'comment' => $faker->text,
                    'created_at' => \Carbon\Carbon::now()->toDateTimeString(),
                ]);
            }

        }
    }
}
