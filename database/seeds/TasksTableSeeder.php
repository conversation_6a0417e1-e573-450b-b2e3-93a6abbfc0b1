<?php

use Illuminate\Database\Seeder;

class TopicsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = \Faker\Factory::create();
        $userIDs = \Illuminate\Support\Facades\DB::table('users')->pluck('id')->toArray();
        $projectIDs = \Illuminate\Support\Facades\DB::table('projects')->pluck('id')->toArray();

        foreach (range(0,count($projectIDs)-1) as $index) {

            $num_of_topics = rand(300,500);

            for($i = 0; $i < $num_of_topics; $i++){

                $status_id = $faker->randomElement(\App\Topic::$statuses);
                $completed_at = $status_id === \App\Topic::$statuses['completed'] ? \Carbon\Carbon::now()->toDateTimeString() : null;

                \Illuminate\Support\Facades\DB::table('topics')->insert([
                    'user_id' => $faker->randomElement($userIDs),
                    'project_id' => $projectIDs[$index],
                    'title' => $faker->sentence,
                    'description' => $faker->text,
                    'priority_id' => $faker->randomElement(\App\Topic::$priorities),
                    'type_id' => $faker->randomElement(\App\Topic::$types),
                    'status_id' => $status_id,
                    'created_at' => \Carbon\Carbon::now()->toDateTimeString(),
                    'completed_at' => $completed_at
                ]);
            }

        }
    }
}
