<?php

use Illuminate\Database\Seeder;

class ProjectUsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $faker = \Faker\Factory::create();
        $userIDs = \Illuminate\Support\Facades\DB::table('users')->pluck('id')->toArray();
        $projectIDs = \Illuminate\Support\Facades\DB::table('projects')->pluck('id')->toArray();

        foreach (range(0,count($projectIDs)-1) as $index) {
            \Illuminate\Support\Facades\DB::table('project_users')->insert([
                'user_id' => $faker->randomElement($userIDs),
                'project_id' => $projectIDs[$index],
                'type_id' => $faker->randomElement(\App\ProjectUser::$types),
                'created_at' => \Carbon\Carbon::now()->toDateTimeString()
            ]);
        }
    }
}
