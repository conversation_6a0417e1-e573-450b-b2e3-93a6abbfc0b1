APP_NAME="Track"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=

OPENAI_API_KEY=
GEMINI_API_KEY=

LOG_CHANNEL=stack

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

ELASTICSEARCH_HOST=

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=mx.posluh.hr
MAIL_PORT=25
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=RfkAY3p4xSQB
MAIL_ENCRYPTION=null
MAIL_FROM_NAME="Track"
MAIL_FROM_ADDRESS="<EMAIL>"
DEVELOPER_EMAIL=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
