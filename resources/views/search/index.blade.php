@extends('layouts.app')
@section('title') {{ __('custom.searchPage') }} - @endsection
@section('content')
    <div class="container">

        @include('partials/categories_modal', ['header' => __('custom.selectCategories'), 'action' => __('custom.choose'), 'categories' => $categories, 'selected_categories' => $selected_categories])

        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.searchPage') }}</span>
                    </div>

                    <div class="card-body fixed-height-body">

                        <div class="row mb-3">
                            <div class="col-lg-6 mb-2 mb-lg-0">
                                <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                    <li class="nav-item">
                                        <a data-id="search" class="tab-switch nav-link {{ $tab == 'search' ? 'active' : '' }}" id="pills-search-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-active" aria-selected="true">
                                            {{ 'Pretraga' }}
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a data-id="chat" class="tab-switch nav-link {{ $tab == 'chat' ? 'active' : '' }}" id="pills-chat-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-active" aria-selected="true">
                                            {{ 'Chat' }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-lg-4 pt-2 mb-2 mb-lg-0"></div>
                            <div class="col-lg-2 mb-2 mb-lg-0">
                                <a id="category-picker" class="btn btn-default float-lg-right form-control" href="#">
                                    {{ __('custom.selectCategories') }}
                                </a>
                            </div>
                        </div>

                        <hr/>

                        <div id="tab-content">
                            <div id="tab-search" style="@if($tab == 'chat') display:none; @endif">
                                <form id="search-form" method="post" action="{{ route('search.ai') }}">
                                    @csrf
                                    <input class="categories" type="hidden" name="categories" value="{{ implode(',', $selected_categories_ids) }}"/>
                                    <input type="hidden" name="tab" value="search"/>

                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="input-group mb-3">
                                                <input placeholder="{{ __('custom.searchPlaceholder') }}" type="text" class="form-control" name="query" id="query" value="{{ $query ?? '' }}">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary">{{ __('custom.search') }}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <div class="row mt-3">
                                    @if(!empty($comments) && $comments->count())
                                        <div class="col-lg-12">
                                            <table class="table table-striped table-bordered comments-table">
                                                <tbody>
                                                @foreach ($comments as $_i => $_comment)

                                                    <tr id="comment{{$_comment->id}}">
                                                        <td @if($_comment->hasDocumentation()) class="alert-warning" @endif>
                                                    <span class="font-weight-bold">
                                                        <img class="user-avatar" src="{{ Avatar::create($_comment->user->name)->toBase64() }}" />
                                                        <a class="comment-link" href="{{ route('comments.show', $_comment) }}">
                                                            {{ $_comment->user->name }} @ {{ $_comment->updated_at }}
                                                        </a>
                                                    </span>

                                                            @if(auth()->user()->id == 1)
                                                                <span class="float-right">
                                                            {{ $_comment->src }}
                                                        </span>
                                                            @endif

                                                            <hr/>

                                                            @if($_comment->referencedComment)

                                                                <div class="bordered-div mb-4" style="background: @if($viewData['themeName'] == 'default-dark') #444444 @elseif($_i%2 == 0) #ffffff @else #f7f7f7 @endif">
                                                                    <img class="user-avatar" src="{{ Avatar::create($_comment->referencedComment->user->name)->toBase64() }}" />
                                                                    <a class="comment-link" href="{{ route('comments.show', $_comment->referencedComment) }}">
                                                            <span class="font-weight-bold">
                                                                {{ $_comment->referencedComment->user->name }} @ {{ $_comment->referencedComment->updated_at }}
                                                            </span>
                                                                    </a>

                                                                    <hr/>

                                                                    @if(!empty(strip_tags($_comment->referencedComment->comment, '<img>')))
                                                                        <div class="referenced-comment mb-2">
                                                                            <div class="comment-content mb-4">
                                                                                {!! $_comment->referencedComment->comment !!}
                                                                                @if($_comment->referencedComment->hasDocumentation())
                                                                                    <div>
                                                                                        <ul>
                                                                                            <li>
                                                                                                <label class="font-weight-bold">
                                                                                                    {{ __('custom.documentation') }}:
                                                                                                    <a target="_blank" href="{{ route('documentation.show', [$_comment->referencedComment->documentation->id]) }}">
                                                                                                        {{ $_comment->referencedComment->documentation->title }}
                                                                                                    </a>
                                                                                                </label>
                                                                                            </li>
                                                                                        </ul>
                                                                                    </div>
                                                                                @endif

                                                                                @if(count($_comment->referencedComment->uploads))
                                                                                    <ul>
                                                                                        @foreach($_comment->referencedComment->uploads as $_upload)
                                                                                            <li>
                                                                                                <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                                                                    {{ $_upload->name }}
                                                                                                </a>
                                                                                            </li>
                                                                                        @endforeach
                                                                                    </ul>
                                                                                @endif
                                                                            </div>
                                                                            <a class="expand-comment d-none">Prikaži komentar</a>
                                                                        </div>

                                                                    @endif
                                                                </div>
                                                            @endif

                                                            @if(!empty(strip_tags($_comment->comment, '<img>')))
                                                                <div class="comment-content mb-4">
                                                                    {!! $_comment->comment !!}
                                                                </div>
                                                            @endif
                                                            @if($_comment->hasDocumentation())
                                                                <div>
                                                                    <ul>
                                                                        <li>
                                                                            <label class="font-weight-bold">
                                                                                {{ __('custom.documentation') }}:
                                                                                <a target="_blank" href="{{ route('documentation.show', [$_comment->documentation->id]) }}">
                                                                                    {{ $_comment->documentation->title }}
                                                                                </a>
                                                                            </label>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            @endif
                                                            @if(count($_comment->uploads))
                                                                <ul>
                                                                    @foreach($_comment->uploads as $_upload)
                                                                        <li>
                                                                            <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                                                {{ $_upload->name }}
                                                                            </a>
                                                                        </li>
                                                                    @endforeach
                                                                </ul>
                                                            @endif
                                                            <hr/>
                                                            <strong>Predmet:</strong> <a href="{{ route('topics.show', $_comment->topic) }}">{{ $_comment->topic->title }}</a>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @elseif(!empty($q))
                                        <div class="col-lg-12">
                                            Nema rezultata za pretragu.
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div id="tab-chat" style="@if($tab == 'search') display:none; @endif">
                                <form id="search-form" method="post" action="{{ route('search.ai') }}">
                                    @csrf
                                    <input class="categories" type="hidden" name="categories" value="{{ implode(',', $selected_categories_ids) }}"/>
                                    <input type="hidden" name="tab" value="chat"/>

                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="input-group mb-3">
                                                <input placeholder="{{ __('custom.askPlaceholder') }}" type="text" class="form-control" name="question" id="question" value="{{ $question ?? '' }}">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary">{{ __('custom.send') }}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <div class="col-lg-12 mt-3">
                                    <div class="row mt-3" id="loader" style="display:none;">
                                        <div class="col-lg-12 mt-5 d-flex justify-content-center">
                                            <img src="{{ url('/images/loader.gif') }}" alt="Loading..." style="max-height: 100px;">
                                        </div>
                                    </div>

                                    @if(!empty($answer))
                                        <div class="row response-box">
                                            <div class="card w-100">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-lg-12">
                                                            {!! $answer !!}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(function() {

            $('.tab-switch').on('click', function(e){
                let view = $(e.target).data('id');
                $('#tab-' + view).show().siblings().hide();
            });

            // categories
            $('#submit-categories').on('click', function (e) {
                let categories = $("#category-tree").jstree("get_checked", null, true);
                $('.categories').val(categories);

                $('#categoriesModal').modal('hide');

                // if tab search is active
                if($('#tab-search').is(':visible')) {
                    if($('#query').val()) {
                        $('#search-form').submit();
                    }
                } else {
                    if($('#question').val()) {
                        $('#chat-form').submit();
                    }
                }
            });

            $('#chat-form, #search-form').on('submit', function(e) {
                let submitButtons = $('button[type=submit]');
                let loader = $('#loader');
                let responseBox = $('.response-box');

                submitButtons.prop('disabled', true);
                submitButtons.addClass('disabled');

                loader.show();

                if (responseBox.length) {
                    responseBox.hide();
                }

                $('input[name="question"]').blur();
            });


            // otherwise images escape containers
            $(".comment-content table").wrap( "<div class='table-responsive'></div>" );
            $("img").addClass("img-responsive");
            $("img").css("max-width", "100%");
            $("img").css("height", "auto");
            $("video").css("max-width", "100%");
            $("video").css("height", "auto");
        });

    </script>
@endpush
