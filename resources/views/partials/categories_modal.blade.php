<div id="categoriesModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $header }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <input class="form-control" id="category-search" type="text" placeholder="Pretraži...">
                        <div class="mt-3" id="category-tree"></div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button id="submit-categories" type="button" class="btn btn-primary">{{ $action }}</button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        $(function() {

            $('#category-picker').on('click', function(e){
                $('#categoriesModal').modal();
            }).on("select2:unselecting", function(e) {   // following prevents select2 from opening on "clear"
                    $(this).data('state', 'unselected');
                }).on("select2:open", function(e) {
                if ($(this).data('state') === 'unselected') {
                    $(this).removeData('state');

                    var self = $(this);
                    setTimeout(function() {
                        self.select2('close');
                    }, 1);
                }
            });

            @if(!empty($cascade))
                $('#category-tree')
                    .jstree({
                        core : {
                            data : @json($selected_categories),
                            themes: {
                                name: "{{ $viewData['themeName'] }}",
                                dots: true,
                                icons: true
                            },
                        },
                        checkbox : {
                            three_state : false
                        },
                        plugins : ['checkbox', 'search']
                    })
                    .on('changed.jstree', function (e, data) {
                        if(data.action === 'select_node'){
                            // select parent and the event will propagate further
                            $(this).jstree(true).select_node(data.node.parent);
                        }
                    });
            @else
                $('#category-tree')
                    .on("init.jstree", function (e, data) {
                        data.instance.settings.checkbox.cascade = '';
                    })
                    .jstree({
                        core : {
                            data : @json($selected_categories),
                            themes: {
                                name: "{{ $viewData['themeName'] }}",
                                dots: true,
                                icons: true
                            },
                        },
                        checkbox : {
                            three_state : false,
                        },
                        plugins : ['checkbox', 'search']
                    });
                setTimeout(function () {
                    $('#category-tree').jstree(true).settings.checkbox.cascade = "down";
                }, 500);
            @endif



            let to = false;
            $('#category-search').keyup(function () {
                if(to) { clearTimeout(to); }
                to = setTimeout(function () {
                    var v = $('#category-search').val();
                    $('#category-tree').jstree('search', v);
                }, 100);
            });
        })
    </script>
@endpush
