@extends('layouts.app')
@section('title') {{ __('custom.categories') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.categories') }}</span>
                        <a class="btn btn-primary float-right" href="{{ route('categories.uncategorized') }}">
                            {{ __('custom.unCategorized') }}
                            @if(!empty($viewData['uncategorizedItemsCount']))
                                <span class="badge badge-pill badge-danger task-count-badge">
                                    {{ $viewData['uncategorizedItemsCount'] }}
                                </span>
                            @endif
                        </a>
                    </div>

                    <div class="card-body fixed-height-body">
                        @if(!empty($categories))
                            <div class="row">
                                <div class="col-lg-6">
                                    <input class="form-control" id="category-search" type="text" placeholder="Pretraži...">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="mt-3" id="category-tree"></div>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-warning">
                                {{ __('custom.noCategoriesExist') }}
                            </div>
                        @endif
                        <div class="row mt-4">
                            <div class="col-lg-12">
                                @if(!empty($categories))
                                    <a class="btn btn-default category-crud-button" id="edit-category">{{ __('custom.editSelectedCategory') }}</a>
                                    <a class="btn btn-default category-crud-button" id="delete-category">{{ __('custom.deleteSelectedCategory') }}</a>
                                @endif
                                <button class="btn btn-primary category-crud-button" id="add-category">{{ __('custom.addNewCategory') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="edit-category-modal" class="modal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <form autocomplete="off" method="post" action="{{ route('categories.update') }}">
                        @csrf
                        {{method_field('PUT')}}
                        <div class="modal-header">
                            <h5 class="modal-title">{{ __('custom.editSelectedCategory') }}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="edit-category-title">{{ __('custom.title') }}</label>
                                        <input autocomplete="off" name="title" required="required" id="edit-category-title" type="text" class="form-control" value="">
                                        <input name="id"  id="edit-category-id" type="hidden" value="">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-primary">{{ __('custom.submit') }}</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div id="delete-category-modal" class="modal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <form autocomplete="off" method="post" action="{{ route('categories.destroy') }}">
                        @csrf
                        {{method_field('DELETE')}}
                        <div class="modal-header">
                            <h5 class="modal-title">{{ __('custom.deleteSelectedCategory') }}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                {{ __('custom.deletingCategoryWillDeleteSubcategories') }}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <input name="id"  id="delete-category-id" type="hidden" value="">
                            <button type="submit" class="btn btn-primary">{{ __('custom.confirm') }}</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div id="add-category-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form autocomplete="off" method="post" action="{{ route('categories.store') }}">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('custom.addNewCategory') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <p class="alert alert-warning" id="parent-category-p"><strong>{{ __('custom.parent') }}:</strong> <span id="parent-category"></span></p>
                                    <p>
                                        <input id="add-category-title" placeholder="Unesi naziv..." autocomplete="off" name="title" required="required" type="text" class="form-control" value="">
                                        <input name="parent_id"  id="parent-category-id" type="hidden" value="">
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">{{ __('custom.submit') }}</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    <script>
        $(function() {

            $('#category-tree')
                .jstree({
                    core : {
                        data : @json($categories),
                        themes: {
                            name: "{{ $viewData['themeName'] }}",
                            dots: true,
                            icons: true
                        },
                        multiple: false,
                        check_callback: true
                    },
                    checkbox : {
                        three_state : false,
                        cascade : "none"
                    },
                    plugins : ['checkbox', 'search', 'dnd']
                })
                .on('move_node.jstree', function (e,data) {
                    $.post( "{{ route('categories.update.parent') }}",
                        {
                            'id': data.node.id,
                            'parent_id': data.parent === "#" ? 0 : data.parent ,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function(data) {
                            alert("{{trans('custom.categoryMoveSuccess')}}");
                        }
                    );
                });
            setTimeout(function () {
                $('#category-tree').jstree(true).settings.checkbox.cascade = "down";
            }, 500);

            let to = false;
            $('#category-search').keyup(function () {
                if(to) { clearTimeout(to); }
                to = setTimeout(function () {
                    var v = $('#category-search').val();
                    $('#category-tree').jstree('search', v);
                }, 100);
            });

            $('#edit-category').on('click', function(e){
                let selectedCategories = $("#category-tree").jstree("get_selected", true);
                if(selectedCategories.length){
                    $('#edit-category-id').val(selectedCategories[0].id);
                    $('#edit-category-title').val(selectedCategories[0].text);
                    $('#edit-category-modal').modal();
                    $('#edit-category-title').focus();
                }
                else{
                    alert("{{trans('custom.youHaveToSelectACategory')}}");
                }

            });

            $('#delete-category').on('click', function(e){
                let selectedCategories = $("#category-tree").jstree("get_selected", true);
                if(selectedCategories.length){
                    $('#delete-category-id').val(selectedCategories[0].id);
                    $('#delete-category-modal').modal();
                }
                else{
                    alert("{{trans('custom.youHaveToSelectACategory')}}");
                }

            });

            $('#add-category').on('click', function(e){
                let selectedCategories = $("#category-tree").jstree("get_selected", true);

                if(selectedCategories.length){
                    $('#parent-category-id').val(selectedCategories[0].id);
                    $('#parent-category').html(selectedCategories[0].text);
                    $('#parent-category-p').show();
                }
                else{
                    $('#parent-category-p').hide();
                }

                $('#add-category-modal').modal();
                $('#add-category-title').focus();

            });

        });
    </script>
@endpush
