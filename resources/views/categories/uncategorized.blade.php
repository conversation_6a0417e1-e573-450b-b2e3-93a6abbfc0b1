@extends('layouts.app')
@section('title') {{ __('custom.unCategorized') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.unCategorized') }} </span>
                    </div>

                    <div class="card-body fixed-height-body">
                        <div class="row">
                            <div class="col-lg-6 mb-2 mb-lg-0">
                                <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                    <li class="nav-item">
                                        <a data-id="topic" class="tab-switch nav-link active" id="pills-active-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-active" aria-selected="true">
                                            {{ __('custom.topics') }}
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a data-id="documentation" class="tab-switch nav-link" id="pills-completed-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-active" aria-selected="true">
                                            {{ __('custom.documentation') }}
                                        </a>
                                    </li>
                                </ul>

                            </div>
                            <div class="col-lg-4 pt-2 mb-2 mb-lg-0">

                            </div>
                            <div class="col-lg-2 mb-2 mb-lg-0">
                                <a id="category-picker" class="btn btn-primary form-control" href="#">
                                    {{ __('custom.assignCategories') }}
                                </a>
                            </div>
                        </div>
                        <hr/>

                        <div id="topics-div">
                            <table class="table table-borderless table-striped" id="uncategorized-topics-table">
                                <thead>
                                <tr>
                                    <th></th>
                                    <th>id</th>
                                    <th>{{ __( 'custom.topic') }}</th>
                                    <th>{{ __( 'custom.type') }}</th>
                                    <th>{{ __( 'custom.createdAt') }}</th>
                                    <th>{{ __( 'custom.status') }}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>

                        <div id="documentation-div" class="d-none">
                            <table class="table table-borderless table-striped" id="uncategorized-documentation-table">
                                <thead>
                                <tr>
                                    <th></th>
                                    <th>id</th>
                                    <th>{{ __( 'custom.documentation') }}</th>
                                    <th>{{ __( 'custom.createdAt') }}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div id="categoriesModal" class="modal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('custom.assignCategories') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <input class="form-control" id="category-search" type="text" placeholder="Pretraži...">
                                <div class="mt-3" id="category-tree"></div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="assign-submit" type="button" class="btn btn-primary">{{ __('custom.assign') }}</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(function() {

            let type = 'topic';

            let topicsTable = initTopicsDataTable();
            let documentationTable = initDocumentationDataTable();

            $('.tab-switch').on('click', function(e){

                type = $(e.target).data('id');

                if(type === 'topic'){
                    $('#topics-div').removeClass('d-none');
                    $('#documentation-div').addClass('d-none');
                }
                else if(type === 'documentation'){
                    $('#topics-div').addClass('d-none');
                    $('#documentation-div').removeClass('d-none');
                }
            });

            $('#category-tree')
                .on("init.jstree", function (e, data) {
                    data.instance.settings.checkbox.cascade = '';
                })
                .jstree({
                    core : {
                        data : @json($categories),
                        themes: {
                            name: "{{ $viewData['themeName'] }}",
                            dots: true,
                            icons: true
                        },
                    },
                    checkbox : {
                        three_state : false,
                    },
                    plugins : ['checkbox', 'search']
                })
                .on('changed.jstree', function (e, data) {
                    if(data.action === 'select_node'){
                        // select parent and the event will propagate further
                        $(this).jstree(true).select_node(data.node.parent);
                    }
                });
            setTimeout(function () {
                $('#category-tree').jstree(true).settings.checkbox.cascade = "up";
            }, 500);

            let to = false;
            $('#category-search').keyup(function () {
                if(to) { clearTimeout(to); }
                to = setTimeout(function () {
                    var v = $('#category-search').val();
                    $('#category-tree').jstree('search', v);
                }, 100);
            });

            $('#category-picker').on('click', function(e){
                let count = false;

                if(type === "topic"){
                    if(topicsTable.rows({selected: true}).data().toArray().length){
                        count = true;
                    }
                }

                if(type === "documentation"){
                    if(documentationTable.rows({selected: true}).data().toArray().length){
                        count = true;
                    }
                }

                if(count){
                    $('#category-tree').jstree("deselect_all");
                    $('#categoriesModal').modal();
                }
                else{
                    alert("{{ __('custom.youHaveToSelectAnItem') }}");
                }

            });

            $('#assign-submit').on('click', function(){
                let selectedCategories = $("#category-tree").jstree("get_checked",null,true);
                let selectedItems = null;

                if(type === "topic"){
                    selectedItems = topicsTable.rows({selected: true}).data().toArray().map(item => item.id);
                }

                if(type === "documentation"){
                    selectedItems = documentationTable.rows({selected: true}).data().toArray().map(item => item.id);
                }

                if(type === 'topic')
                {
                    $.post( "{{ route('categories.assignTopics') }}",
                        {
                            'topics': selectedItems,
                            'categories': selectedCategories,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function(data) {
                            location.reload();
                        }
                    );
                }

                if(type === 'documentation')
                {
                    $.post( "{{ route('categories.assignDocumentation') }}",
                        {
                            'documentation': selectedItems,
                            'categories': selectedCategories,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function(data) {
                            location.reload();
                        }
                    );
                }
            });

            function initTopicsDataTable()
            {
                return $('#uncategorized-topics-table').DataTable({
                    dom: 'Blfrtip',
                    buttons: [
                        'selectAll',
                        'selectNone'
                    ],
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    responsive: {
                        details: false
                    },
                    pageLength: 10,
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: '{!! route('categories.listUncategorized', ['type' => 'topic']) !!}',
                        cache: true
                    },
                    columns: [
                        {data: null},
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            sortable: false,
                            render: function(data, type, row) {
                                return '<a target="_blank" class="dt-link" href="'+ row['show_link'] +'">' + data + '</a>';
                            }
                        },
                        {
                            data: 'type_id', name: 'type_id',sortable: false,
                        },
                        {
                            data: 'created_at', name: 'created_at', sortable: false,
                        },
                        {
                            data: 'status_id',
                            sortable: false,
                            type: 'html',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['status']+'</span>';
                            }
                        },
                    ],
                    columnDefs: [
                        {
                            targets: 0,
                            data: null,
                            defaultContent: '',
                            orderable: false,
                            className: 'select-checkbox',
                        },
                        {
                            targets: [ 1 ],
                            visible: false,
                        },
                        {
                            targets: [ 0, 1, 3, 4, 5],
                            searchable: false
                        },
                    ],
                    select: {
                        style:    'multi',
                        selector: 'td:first-child'
                    },
                });
            }

            function initDocumentationDataTable()
            {
                return $('#uncategorized-documentation-table').DataTable({
                    dom: 'Blfrtip',
                    buttons: [
                        'selectAll',
                        'selectNone'
                    ],
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    responsive: {
                        details: false
                    },
                    pageLength: 10,
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: '{!! route('categories.listUncategorized', ['type' => 'documentation']) !!}',
                        cache: true
                    },
                    columns: [
                        {data: null},
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            render: function(data, type, row) {
                                return '<a target="_blank" class="dt-link" href="'+ row['show_link'] +'">' + data + '</a>';
                            }
                        },
                        {
                            data: 'created_at', name: 'created_at',
                        },
                    ],
                    columnDefs: [
                        {
                            targets: 0,
                            data: null,
                            defaultContent: '',
                            orderable: false,
                            className: 'select-checkbox',
                        },
                        {
                            targets: [ 1 ],
                            visible: false,
                        },
                        {
                            targets: [ 0, 1, 3],
                            searchable: false
                        },
                    ],
                    select: {
                        style:    'multi',
                        selector: 'td:first-child'
                    },
                });
            }

        });
    </script>
@endpush
