<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') {{ config('app.name', 'Laravel') }}</title>

    <link rel="shortcut icon" type="image/png" href="/favicon.png"/>

    <script>
        window.theme = {!! json_encode($viewData['themeName']) !!};
    </script>

    <!-- Styles -->
    <link href="{{ asset('css/'.$viewData['themeName'].'/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/'.$viewData['themeName'].'/dataTables.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/'.$viewData['themeName'].'/datepicker.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/'.$viewData['themeName'].'/select2.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/'.$viewData['themeName'].'/prism.css') }}" rel="stylesheet">
    <link href="{{ asset('css/'.$viewData['themeName'].'/main.css') }}?1223453353362271" rel="stylesheet">
    <link href="{{ asset('css/jstree/themes/'.$viewData['themeName'].'/style.min.css') }}" rel="stylesheet">
    <link href="{{ asset('css/common/main.css') }}?12122512663" rel="stylesheet">


{{--    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.0/css/all.css" integrity="sha384-lZN37f5QGtY3VHgisS14W3ExzMWZxybE1SJSEsQp9S+oqd12jhcu+A56Ebc1zFSJ" crossorigin="anonymous">--}}
</head>
<body>
    <div id="app">
        <nav class="navbar sticky-top navbar-expand-md @if($viewData['themeName'] == 'default-dark') navbar-dark bg-dark @else navbar-light bg-light @endif shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    {{ config('app.name', 'Laravel') }}
                </a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon"></span>
                    @if(!empty($viewData['unreadNotificationsCount']))
                        <span class="badge badge-pill badge-danger task-count-menu-badge">
                             {{ $viewData['unreadNotificationsCount'] }}
                        </span>
                    @endif
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav mr-auto">

                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ml-auto">
                        <!-- Authentication Links -->
                        @guest
                            <li class="nav-item">
                                <a class="nav-link" href="{{ route('login') }}">{{ __('auth.login') }}</a>
                            </li>
                            @if (Route::has('register'))
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ route('register') }}">{{ __('auth.register') }}</a>
                                </li>
                            @endif
                        @else
                            <li class="nav-item dropdown">
                                <div class="dropdown-hoverable dropdown">
                                    <a class="nav-link dropdown-toggle @if(strpos(Route::currentRouteName(), 'topics') !== false) active @endif" href="{{ route('topics') }}" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {{ __('custom.topics') }}
                                    </a>

                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a class="dropdown-item" href="{{ route('topics') }}">{{ __('custom.allTopicsNav') }}</a>
                                        <a class="dropdown-item" href="{{ route('topics.create') }}">{{ __('custom.newTopic') }}</a>
                                    </div>
                                </div>
                            </li>
                            <li class="divider-vertical"></li>
                            @if(Auth::user()->isAdmin())
                                <li class="nav-item">
                                    <a class="nav-link @if(strpos(Route::currentRouteName(), 'search') !== false) active @endif" href="{{ route('search') }}">
                                        {{ __('custom.searchPage') }} <small><sup class="badge badge-success badge-pill">BETA</sup></small>
                                    </a>
                                </li>
                                <li class="divider-vertical"></li>
                                <div class="dropdown-hoverable dropdown">
                                    <a href="{{ route('categories.list') }}" class="nav-link dropdown-toggle @if(strpos(Route::currentRouteName(), 'categories') !== false) active @endif" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {{ __('custom.categories') }}
                                    </a>

                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a class="dropdown-item" href="{{ route('categories.list') }}">{{ __('custom.categories') }}</a>
                                        <a class="dropdown-item" href="{{ route('categories.uncategorized') }}">
                                            {{ __('custom.unCategorized') }}
                                            @if(!empty($viewData['uncategorizedItemsCount']))
                                                <span class="badge badge-pill badge-danger task-count-badge">
                                                    {{ $viewData['uncategorizedItemsCount'] }}
                                                </span>
                                            @endif
                                        </a>
                                    </div>
                                </div>
                                <li class="divider-vertical"></li>
                            @endif
                            <li class="nav-item">
                                <a class="nav-link @if(strpos(Route::currentRouteName(), 'documentation') !== false) active @endif" href="{{ route('documentation') }}">
                                    {{ __('custom.documentation') }}
                                </a>
                            </li>
                            <li class="divider-vertical"></li>
                            <li class="nav-item dropdown">
                                <div class="dropdown-hoverable dropdown">
                                    <a href="{{ route('profile') }}" class="nav-link dropdown-toggle" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {{ Auth::user()->name }}
                                        @if(!empty($viewData['unreadNotificationsCount']))
                                            <span class="badge badge-pill badge-danger task-count-badge">
                                                    {{ $viewData['unreadNotificationsCount'] }}
                                                </span>
                                        @endif
                                    </a>

                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        <a class="dropdown-item" href="{{ route('notifications') }}">
                                            {{ __('custom.notificationsHeader') }}
                                            @if(!empty($viewData['unreadNotificationsCount']))
                                                <span class="badge badge-pill badge-danger task-count-badge">
                                                    {{ $viewData['unreadNotificationsCount'] }}
                                                </span>
                                            @endif
                                        </a>
                                        <a class="dropdown-item" href="{{ route('profile') }}">{{ __('custom.myProfile') }}</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="{{ route('logout') }}"
                                           onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                            {{ __('auth.logout') }}
                                        </a>

                                        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                            @csrf
                                        </form>
                                    </div>
                                </div>
                            </li>

                        @endguest
                        {{--<li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                {!! app()->getLocale() !!}
                            </a>
                            <div class="dropdown-menu" aria-labelledby="navbarDropdown">
                                @foreach(array_diff( $viewData['locales'], [app()->getLocale()] ) as $locale)
                                    <a class="dropdown-item" href="{!! route('locale', ['id' => $locale]) !!}">{!! $locale !!}</a>
                                @endforeach
                            </div>
                        </li>--}}

                    </ul>
                </div>
            </div>
        </nav>

        <main class="py-4">
            @if (session('status'))
                <div class="container">
                    <div class="alert alert-{{ session('status_type') ?: 'success' }}" role="alert">
                        {{ session('status') }}
                    </div>
                </div>
            @endif
            @yield('content')
        </main>
    </div>

    <!-- Scripts -->


    <script src="{{ asset('js/jQuery.min.js') }}"></script>
    <script src="{{ asset('js/popper.min.js') }}"></script>
    <script src="{{ asset('js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('js/datepicker.min.js') }}"></script>

    <script src="{{ asset('/vendor/tinymce/js/tinymce/tinymce.min.js') }}"></script>
    <script src="{{ asset('js/select2.min.js') }}"></script>


    @if(app()->getLocale() == "hr")
        <script src="{{ asset('js/datepicker-hr.js') }}"></script>
        <script src="{{ asset('/vendor/tinymce/js/tinymce/langs/tinymce-hr.js') }}"></script>
    @endif

    <script src="{{ asset('js/dataTables.min.js') }}"></script>
    <script src="{{ asset('js/jstree.min.js') }}"></script>

    <script src="{{ asset('js/main.js') }}?id=122134"></script>

    <script src="{{ asset('js/prism.js') }}"></script>

    @stack('scripts')

</body>
</html>
