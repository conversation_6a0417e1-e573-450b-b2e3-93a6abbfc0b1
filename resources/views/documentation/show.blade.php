@extends('layouts.app')
@section('title') {{ $documentation->title }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-lg-10">
                                {{ $documentation->title }}
                            </div>
                            @if(Auth::user()->isAdmin() || $documentation->canEdit())
                                <div class="col-lg-2 pt-2 pt-lg-0">
                                    <div class="dropdown show">
                                        <a class="btn btn-primary dropdown-toggle btn-block" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            {{ __('custom.chooseAction') }}
                                        </a>
                                        <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                            @if(Auth::user()->isAdmin())
                                                <button style="cursor: pointer;" id="edit-categories" class="dropdown-item">{{ __('custom.editCategories') }}</button>
                                            @endif
                                            @if($documentation->canEdit())
                                                    <a class="dropdown-item" href="{!! route('documentation.edit', [$documentation->id]) !!}">{{ __('custom.editDocumentation') }}</a>
                                                @endif
                                            @if(Auth::user()->isDocumentationOwner($documentation))
                                                <a class="dropdown-item pleaseConfirm" data-confirmation="{{ __('custom.pleaseConfirm') }}" href="{!! route('documentation.destroy', [$documentation->id]) !!}">{{ __('custom.deleteDocumentation') }}</a>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <div class="col-lg-12">
                                @foreach($documentation->categories as $_category)
                                    <span class="badge badge-primary category-badge" data-id="{{ $_category->id }}">
                                        #{{ $_category->title }}
                                    </span>
                                @endforeach
                            </div>
                        </div>


                    </div>

                    <div class="card-body">

                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-bordered">
                                <thead>
                                <tr>
                                    <td>{{ __('custom.createdAt') }}</td>
                                    <td>{{ __('custom.updatedAt') }}</td>
                                    <td>{{ __('custom.creator') }}</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>{{ $documentation->created_at }}</td>
                                    <td>{{ $documentation->updated_at }}</td>
                                    <td>{{ $documentation->user->name ?: '' }}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        @if($documentation->content || count($documentation->uploads))
                            <div class="alert documentation-body">
                                {!! $documentation->content !!}
                                @if(count($documentation->uploads))
                                    <div class="mt-2">
                                        <span class="font-weight-bold">{{ __('custom.files') }}</span>
                                        <ul>
                                            @foreach($documentation->uploads as $_upload)
                                                <li>
                                                    <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                        {{ $_upload->name }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                            <hr/>
                            <i>Zadnje uređivano: {{ $documentation->lastEditedByUser->name }}, {{ $documentation->updated_at_time }}</i>
                        @endif
                    </div>
                </div>
            </div>
            <div id="categories-modal" class="modal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">{{ __('custom.selectCategories') }}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <input class="form-control" id="category-search" type="text" placeholder="Pretraži...">
                                    <div class="mt-3" id="category-tree"></div>
                                </div>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button id="edit-categories-submit" type="button" class="btn btn-primary">{{ __('custom.submit') }}</button>
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="categories-edited-success" class="modal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">{{ __('custom.success') }}</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="alert alert-success">
                                        {{ __('custom.categoriesEdited') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="category-documentation-modal" class="modal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title" id="category-documentation-header"></h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div id="category-documentation-table-div" style="padding-top:1px;">
                                        <table class="table table-borderless table-striped" id="category-documentation-table">
                                            <thead>
                                            <tr>
                                                <th>id</th>
                                                <th>{{ __( 'custom.title') }}</th>
                                                <th>{{ __( 'custom.createdAt') }}</th>
                                                <th>{{ __( 'custom.updatedAt') }}</th>
                                            </tr>
                                            </thead>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
@endsection

@push('scripts')
            <script>
                $(document).ready(function(){

                    // on category assign, refresh page
                    $('#categories-edited-success').on('hidden.bs.modal', function () {
                        location.reload();
                    });

                    // category badge popup
                    $('.category-badge').on('click', function(e) {

                        let category_id = $(this).data('id');
                        let category_name = $(this).html();

                        $('#category-documentation-header').html(
                            '<span class="badge badge-primary category-badge">'
                            +category_name+
                            '</span>');

                        initDataTable(category_id);

                        $('#category-documentation-modal').modal()
                    })

                    $('#category-documentation-modal').on('hidden.bs.modal', function () {
                        // destroy datatable
                        $('#category-documentation-table').DataTable().destroy();
                    });

                    function initDataTable(category_id)
                    {
                        let route = '{!! route('documentation.list', ['status' => 'active']) !!}';

                        let ajax_route = route+"&topics=&categories="+category_id;

                        $('#category-documentation-table-div').hide();

                        return $('#category-documentation-table').DataTable({
                            language: { url: '{!! route('dataTables.languageFile') !!}' },
                            autoWidth: false,
                            responsive: {
                                details: false

                            },
                            pageLength: 10,
                            order: [],
                            processing: false,
                            serverSide: true,
                            ajax: {
                                url: ajax_route,
                                cache: true
                            },
                            initComplete: function() {
                                $('#category-documentation-table-div').fadeIn(100);
                            },
                            columns: [
                                { data: 'id' },
                                {
                                    data: 'title',
                                    name: 'title',
                                    type: 'html',
                                    render: function(data, type, row) {
                                        return '<a class="dt-link" href="'+ row['show_link'] +'">' + data + '</a>';
                                    }
                                },
                                {
                                    data: 'created_at',
                                    name: 'created_at',
                                },
                                {
                                    data: 'updated_at',
                                    name: 'updated_at',
                                }
                            ],
                            columnDefs: [
                                {
                                    targets: [ 0 ],
                                    visible: false,
                                },
                                {
                                    targets: [ 0 ],
                                    searchable: false
                                },
                            ],
                        });
                    }

                    $('#edit-categories').on('click', function(e){
                        $('#categories-modal').modal();
                    });

                    $('#category-tree')
                        .jstree({
                            core : {
                                data : @json($documentation->getCategoryTree()),
                                themes: {
                                    name: "{{ $viewData['themeName'] }}",
                                    dots: true,
                                    icons: true
                                },
                            },
                            checkbox : {
                                three_state : false,
                            },
                            plugins : ['checkbox', 'search']
                    })
                    .on('changed.jstree', function (e, data) {
                        if(data.action === 'select_node'){
                            // select parent and the event will propagate further
                            $(this).jstree(true).select_node(data.node.parent);
                        }
                    });
                    setTimeout(function () {
                        $('#category-tree').jstree(true).settings.checkbox.cascade = "up";
                    }, 500);

                    let to = false;
                    $('#category-search').keyup(function () {
                        if(to) { clearTimeout(to); }
                        to = setTimeout(function () {
                            var v = $('#category-search').val();
                            $('#category-tree').jstree('search', v);
                        }, 100);
                    });

                    $('#edit-categories-submit').on('click', function(e){
                        let categories = $("#category-tree").jstree("get_checked",null,true);

                        $.post( "{{ route('documentation.editCategories') }}",
                            {
                                'id': '{{ $documentation->id }}',
                                'categories': categories ,
                                '_token': $('meta[name="csrf-token"]').attr('content')
                            },
                            function(data) {
                                $('#categories-modal').modal('hide');
                                $('#categories-edited-success').modal();
                            }
                        );
                    });

                });
            </script>
@endpush
