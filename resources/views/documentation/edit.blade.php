@extends('layouts.app')
@section('title') {{ __('custom.editDocumentation') }} - @endsection
@section('content')

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.editDocumentation') }}</span>
                    </div>

                    <div class="card-body">

                        <form autocomplete="off" method="POST" action="{{ route('documentation.update', [$documentation->id]) }}" enctype="multipart/form-data">
                            @csrf
                            {{method_field('PUT')}}
                            <div class="form-group">
                                <label for="title">
                                    {{ __('custom.title') }}
                                </label>
                                <input id="title" name="title" class="form-control" type="text" value="{{ old('title', $documentation->title) }}">
                            </div>
                            @if(Auth::user()->isDocumentationOwner($documentation))
                                <div class="row">
                                <div class="col-lg-12">
                                    <a data-toggle="collapse" id="add-comment" href="#documentationUsers" role="button" aria-expanded="true" aria-controls="documentationUsers" class="btn btn-default btn-block">
                                        {{ __('custom.selectUsers') }}
                                    </a>
                                    <div id="accordion">
                                        <div id="documentationUsers" class="collapse" aria-labelledby="documentationUsersHeading" data-parent="#accordion">
                                            <div class="card-body">
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-hover table-bordered">
                                                        <thead>
                                                        <tr>
                                                            <td>{{ __('custom.user') }}</td>
                                                            <td> <input type="checkbox" {{ \App\User::all()->count() == $documentation->users->count() ? "checked" : "" }} id="select-all-users-access"> </td>
                                                            <td> <input type="checkbox" {{ \App\User::all()->count() == $documentation->users->where('can_edit', 1)->count() ? "checked" : "" }} id="select-all-users-editing"> </td>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        @foreach(\App\User::where('id', '!=', Auth::id())->get() as $_user)
                                                            <tr>
                                                                <td>{{ $_user->name }}</td>
                                                                <td>
                                                                    <div class="form-check">
                                                                        <input {{ in_array($_user->id, array_keys($documentation_users)) ? "checked" : "" }} class="form-check-input user-access-checkbox" type="checkbox" name="users[{{ $_user->id }}][access]" id="access{{$_user->id}}">
                                                                        <label class="form-check-label" for="access{{$_user->id}}">
                                                                            {{ __('custom.access') }}
                                                                        </label>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div class="form-check">
                                                                        <input {{ !empty($documentation_users[$_user->id]["can_edit"]) ? "checked" : "" }} class="form-check-input user-editing-checkbox" type="checkbox" name="users[{{ $_user->id }}][editing]" id="editing{{$_user->id}}">
                                                                        <label class="form-check-label" for="editing{{$_user->id}}">
                                                                            {{ __('custom.editing') }}
                                                                        </label>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>
                                                @if($errors->has('users'))
                                                    <div class="error">{{ $errors->first('users') }}</div>
                                                @endif
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                            @endif
                            <div class="form-group @if(Auth::user()->isDocumentationOwner($documentation)) mt-2 @endif">
                                <label for="content">{{ __('custom.documentation') }}</label>
                                <textarea class="form-control" id="content" name="content">
                                    {{ old('content', $documentation->content) }}
                                </textarea>
                                @if($errors->has('content'))
                                    <div class="error">{{ $errors->first('content') }}</div>
                                @endif
                            </div>

                            @if(count($documentation->uploads))
                                <span>{{ __('custom.files') }}</span>
                                <ul>
                                    @foreach($documentation->uploads as $_upload)
                                        <li class="existing_upload">
                                            <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                {{ $_upload->name }}
                                            </a>
                                            <input type="hidden" name="existing_uploads[]" value="{{ $_upload->id }}">
                                            <a class="btn btn-xs btn-primary delete-upload" href="#">x</a>
                                        </li>

                                    @endforeach
                                </ul>
                            @endif

                            <div class="form-group">
                                <input id="uploads" type="file" name="uploads[]" multiple>
                                @if($errors->has('uploads'))
                                    <div class="error">{{ $errors->first('uploads') }}</div>
                                @endif
                            </div>

                            <hr/>
                            <a id="category-picker" class="btn btn-default" href="#">
                                {{ __('custom.editCategories') }}
                            </a>

                            <input id="categories" type="hidden" name="categories" value="{{ implode(",", $documentationCategories)  }}"/>

                            <input value="{{ __('custom.submit') }}" type="submit" class="btn btn-primary">

                        </form>


                    </div>
                </div>
            </div>
        </div>
        @include('partials/categories_modal', ['header' => __('custom.selectCategories'), 'action' => __('custom.save'), 'categories' => $categories, 'selected_categories' => $documentation->getCategoryTree(), 'cascade' => 1])
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {

            initializeTextEditor(
                '#content',
                '{{ route('image.upload') }}',
                '{{ csrf_token() }}',
                {{ $viewData['themeName'] == 'default-dark' }}
            );

            $('.delete-upload').on('click', function(e){
                e.preventDefault();
                let question = "{{ __('custom.pleaseConfirm') }}";
                if (window.confirm(question)) {
                    $(this).closest('.existing_upload').remove();
                }
            });

            // otherwise images escape containers
            $(".comment-content table").wrap( "<div class='table-responsive'></div>" );
            $("img").addClass("img-responsive");
            $("img").css("max-width", "100%");
            $("img").css("height", "auto");
            $("video").css("max-width", "100%");
            $("video").css("height", "auto");

            // categories
            $('#submit-categories').on('click', function (e) {
                let categories = $("#category-tree").jstree("get_checked", null, true);

                $('#categories').val(categories);
                $('#categoriesModal').modal('hide');

            });
        });
    </script>
    <script src="/js/partials/documentation_user_privileges_toggle.js"></script>
@endpush
