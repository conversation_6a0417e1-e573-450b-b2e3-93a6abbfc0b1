@extends('layouts.app')
@section('title') {{ __('custom.allDocumentationNav') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.allDocumentationNav') }}</span>
                        <a id="newDocumentation" class="btn btn-primary float-right" href="{{ __(route('documentation.create')) }}">+ {{ __('custom.newDocumentationNav') }}</a>
                    </div>

                    <div class="card-body fixed-height-body">

                        <div class="row">
                            <div id="documentationTopicFilter" class="col-lg-4">
                               <select multiple class="form-control" id="topic-picker">

                               </select>
                            </div>

                            <div class="col-lg-6 mb-2">

                            </div>

                            <div class="col-lg-2 mb-4">
                                <a id="category-picker" class="btn btn-default form-control" href="#">
                                    {{ __('custom.selectCategory') }}
                                </a>
                                <div id="categories-selected-div" class="text-center d-none">
                                    <small>{{ __('custom.categoriesSelectedCount') }}: <span id="selected-categories-count">0</span>
                                        <a class="ml-1" href="#" id="undo-categories">{{ __('custom.undo') }}</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                        <hr style="margin-top: 0"/>

                        <table class="table table-borderless table-striped" id="documentation-table">
                            <thead>
                            <tr>
                                <th>id</th>
                                <th>{{ __( 'custom.title') }}</th>
                                <th>{{ __( 'custom.creator') }}</th>
                                <th>{{ __( 'custom.createdAt') }}</th>
                                <th>{{ __( 'custom.updatedAt') }}</th>
                            </tr>
                            </thead>
                        </table>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="categoriesModal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.selectCategory') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <input class="form-control" id="category-search" type="text" placeholder="Pretraži...">
                            <div class="mt-3" id="category-tree"></div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button id="filter-categories" type="button" class="btn btn-primary">{{ __('custom.filter') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div id="category-documentation-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="category-documentation-header"></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="category-documentation-table-div" style="padding-top:1px;">
                                <table class="table table-borderless table-striped" id="category-documentation-table">
                                    <thead>
                                    <tr>
                                        <th>id</th>
                                        <th>{{ __( 'custom.title') }}</th>
                                        <th>{{ __( 'custom.createdAt') }}</th>
                                        <th>{{ __( 'custom.updatedAt') }}</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(function() {

            let pageLength = 10;

            let categories = [];
            let topics = [];

            let table = initDataTable();

            table.on( 'length.dt', function ( e, settings, len ) {
                pageLength = len;
            } );

            function handleSelectedCategoriesDiv()
            {
                let count = $("#category-tree").jstree("get_checked",null,true).length;

                $('#selected-categories-count').html(count);

                if(count){
                    $('#categories-selected-div').removeClass('d-none');
                }
                else{
                    $('#categories-selected-div').addClass('d-none');
                }
            }

            $('#category-tree')
                .on("init.jstree", function (e, data) {
                    data.instance.settings.checkbox.cascade = '';
                })
                .jstree({
                    core : {
                        data : @json($categories),
                        themes: {
                            name: "{{ $viewData['themeName'] }}",
                            dots: true,
                            icons: true
                        },
                    },
                    checkbox : {
                        three_state : false,
                        cascade : "down"
                    },
                    plugins : ['checkbox', 'search']
                });
            setTimeout(function () {
                $('#category-tree').jstree(true).settings.checkbox.cascade = "down";
            }, 500);

            let to = false;
            $('#category-search').keyup(function () {
                if(to) { clearTimeout(to); }
                to = setTimeout(function () {
                    var v = $('#category-search').val();
                    $('#category-tree').jstree('search', v);
                }, 100);
            });

            $('#filter-categories').on('click', function(e){
                categories = $("#category-tree").jstree("get_checked",null,true);
                table.clear().destroy();
                table = initDataTable();
                handleSelectedCategoriesDiv();
                $('#categoriesModal').modal('hide');
            });

            $('#undo-categories').on('click', function(e){
                $("#category-tree").jstree().deselect_all(true);
                categories = [];
                table.clear().destroy();
                table = initDataTable();
                handleSelectedCategoriesDiv();
                $('#categoriesModal').modal('hide');
            });


            $('#category-picker').on('click', function(e){
                $('#categoriesModal').modal();
            });

            function initDataTable()
            {
                let route = '{!! route('documentation.list') !!}';

                return $('#documentation-table').DataTable({
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    pageLength: pageLength,
                    responsive: {
                        details: false
                    },
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: route + "?categories=" + categories + "&topics=" + topics,
                        cache: true
                    },
                    columns: [
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            render: function(data, type, row) {
                                return !isMobile() ? (
                                    '<a class="dt-link" href="' + row['show_link'] + '" ' +
                                    'data-toggle="popover" data-content="' + row['categories'] + '">' + data + '</a>'
                                ) : '<a class="dt-link" href="' + row['show_link'] + '">' + data + '</a>'
                            }
                        },
                        {
                            data: 'user',
                            sortable: false,
                        },
                        {
                            data: 'created_at',
                            name: 'created_at',
                        },
                        {
                            data: 'updated_at',
                            name: 'updated_at',
                        }
                    ],
                    columnDefs: [
                        {
                            targets: [ 0 ],
                            visible: false,
                        },
                        {
                            targets: [2,3,4],
                            visible: !isMobile()
                        },
                        {
                            targets: [ 0, 2 ],
                            searchable: false
                        },
                    ],
                    drawCallback: function () {
                        $('[data-toggle="popover"]').popover({
                            trigger: 'manual',
                            placement: 'right',
                            html: true
                        }).on('mouseenter', function () {
                            let _this = this;
                            clearTimeout($(_this).data('timeoutId')); // Clear any existing timeout

                            // Set a timeout to show the popover after 200ms
                            let hoverTimeout = setTimeout(function () {
                                $(_this).popover('show');

                                // Ensure popover stays open when hovered
                                $('.popover').on('mouseenter', function () {
                                    clearTimeout($(_this).data('timeoutId'));
                                }).on('mouseleave', function () {
                                    $(_this).popover('hide');
                                });
                            }, 500);
                            $(_this).data('hoverTimeout', hoverTimeout); // Store the timeout ID for hover

                        }).on('mouseleave', function () {
                            let _this = this;
                            clearTimeout($(_this).data('hoverTimeout')); // Clear the hover timeout if mouse leaves early

                            let timeoutId = setTimeout(function () {
                                if (!$('.popover:hover').length) {
                                    $(_this).popover('hide');
                                }
                            }, 100); // Slightly increased timeout for better UX
                            $(_this).data('timeoutId', timeoutId); // Store the timeout ID
                        });
                    }
                });
            }

            function initDocumentationCategoriesDataTable(category_id)
            {
                let route = '{!! route('documentation.list', ['status' => 'active']) !!}';

                let ajax_route = route+"&topics=&categories="+category_id;

                $('#category-documentation-table-div').hide();

                return $('#category-documentation-table').DataTable({
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    responsive: {
                        details: false

                    },
                    pageLength: 10,
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: ajax_route,
                        cache: true
                    },
                    initComplete: function() {
                        $('#category-documentation-table-div').fadeIn(100);
                    },
                    columns: [
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            render: function(data, type, row) {
                                return '<a class="dt-link" href="'+ row['show_link'] +'">' + data + '</a>';
                            }
                        },
                        {
                            data: 'created_at',
                            name: 'created_at',
                        },
                        {
                            data: 'updated_at',
                            name: 'updated_at',
                        }
                    ],
                    columnDefs: [
                        {
                            targets: [ 0 ],
                            visible: false,
                        },
                        {
                            targets: [ 0 ],
                            searchable: false
                        },
                    ],
                });
            }

            // delegate the category badge click event to avoid re-binding
            $(document).on('click', '.category-badge', function(e) {
                // hide any visible popovers
                $('[data-toggle="popover"]').popover('hide');

                let category_id = $(this).data('id');

                if(category_id) {
                    let category_name = $(this).html();

                    $('#category-documentation-header').html(
                        '<span class="badge badge-primary category-badge">'
                        + category_name +
                        '</span>');

                    initDocumentationCategoriesDataTable(category_id);
                    $('#category-documentation-modal').modal();
                }
            });

            // destroy the DataTable when the modal is hidden
            $('#category-documentation-modal').on('hidden.bs.modal', function () {
                $('#category-documentation-table').DataTable().destroy();
            });

            $('#topic-picker').select2({
                placeholder: '{{ __('custom.searchTopics') }}...',
                allowClear: true,
                minimumInputLength: 3,
                ajax: {
                    url: '{{ route('topics.search') }}',
                    dataType: 'json',
                    delay: 200,
                }
            }).on('change', function(e){
                topics = $(this).val();
                table.clear().destroy();
                table = initDataTable();
            });

        });
    </script>
@endpush
