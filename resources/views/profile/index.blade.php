@extends('layouts.app')
@section('title') {{ __('custom.myProfile') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.myProfile') }}</span>
                    </div>

                    <div class="card-body fixed-height-body">
                        <h5>Osobni podaci</h5>
                        <ul>
                            <li>Ime: {{ Auth::user()->name }}</li>
                            <li>Email: {{ Auth::user()->email }}</li>
                            <li>Datum registracije: {{ date('d.m.Y.', strtotime(Auth::user()->created_at)) }}</li>
                            <li>Aktivnih zadataka: <a href="{{ route('topics') }}">{{ $viewData['activeTasksCount'] }}</a></li>
                        </ul>


                            @if($viewData['themeName'] == 'default-dark')
                                <button data-action="off" class="btn btn-default" id="toggle-dark-mode">Isključi tamni način rada</button>
                            @else
                                <button data-action="on" class="btn btn-primary" id="toggle-dark-mode">Uključi tamni način rada</button>
                            @endif

                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('scripts')
    <script>
        $(function() {
            $('#toggle-dark-mode').on('click', function(e){

                let action = $(this).data('action');

                if(action === "on") {
                    let expiration_date = new Date();
                    expiration_date.setFullYear(expiration_date.getFullYear() + 1);
                    document.cookie = "dark-theme=true; path=/; expires=" + expiration_date.toUTCString();
                }
                else if(action === "off") {
                    document.cookie = 'dark-theme=; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                }

                // reload
                location.reload();

            });
        });
    </script>
@endpush
