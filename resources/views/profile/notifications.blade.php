@extends('layouts.app')
@section('title') {{ __('custom.notificationsHeader') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.notificationsHeader') }}</span>
                    </div>

                    <div class="card-body fixed-height-body">

                        <div class="row">
                            <div class="col-lg-12 table-responsive">
                                @if($notifications->count())
                                    <table class="table">

                                        <tbody>
                                        @foreach($notifications as $_notification)
                                            <tr>
                                                <td>
                                                    @if($_notification->data['action'] == \App\Notification::ACTION_TOPIC_CREATE)
                                                        Korisnik {{ $_notification->user->name }} objavljuje novi predmet na kojem sudjelujete:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->data['topic_id']) }}">{{ $_notification->topic->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_TOPIC_EDIT)
                                                        Korisnik {{ $_notification->user->name }} uređuje predmet na kojem sudjelujete:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->data['topic_id']) }}">{{ $_notification->topic->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_TOPIC_COMMENT_CREATE)
                                                        Korisnik {{ $_notification->user->name }} objavljuje novi <a target="_blank" href="{{ route('comments.show', $_notification->comment->id) }}">komentar</a> na predmetu na kojem sudjelujete:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->comment->topic->id) }}">{{ $_notification->comment->topic->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_TOPIC_COMMENT_EDIT)
                                                        Korisnik {{ $_notification->user->name }} uređuje <a target="_blank" href="{{ route('comments.show', $_notification->comment->id) }}">komentar</a> na predmetu na kojem sudjelujete:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->comment->topic->id) }}">{{ $_notification->comment->topic->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_TOPIC_COMMENT_LIKE)
                                                        Korisniku {{ $_notification->user->name }} se sviđa vaš <a target="_blank" href="{{ route('comments.show', $_notification->comment->id) }}">komentar</a> na predmetu na kojem sudjelujete:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->comment->topic->id) }}">{{ $_notification->comment->topic->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_DOCUMENTATION_CREATE)
                                                        Korisnik {{ $_notification->user->name }} dodaje novu dokumentaciju:
                                                        <a target="_blank" href="{{ route('documentation.show', $_notification->documentation->id) }}">{{ $_notification->documentation->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_DOCUMENTATION_EDIT)
                                                        Korisnik {{ $_notification->user->name }} uređuje dokumentaciju:
                                                        <a target="_blank" href="{{ route('documentation.show', $_notification->documentation->id) }}">{{ $_notification->documentation->title }}</a>
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_TOPIC_REMINDER)
                                                        Imate podsjetnik za predmet:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->data['topic_id']) }}">{{ $_notification->topic->title }}</a> - {{ $_notification->data['reminder'] }}
                                                    @elseif($_notification->data['action'] == \App\Notification::ACTION_TASK_EXPIRING)
                                                        Rok za zadatak na kojem sudjelujete uskoro ističe:
                                                        <a target="_blank" href="{{ route('topics.show', $_notification->data['topic_id']) }}">{{ $_notification->topic->title }}</a>
                                                    @endif
                                                </td>
                                                <td>
                                                    {{ date('d.m.Y, H:i', strtotime($_notification->created_at)) }}
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                @else
                                    Nemate novih obavijesti.
                                @endif
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
