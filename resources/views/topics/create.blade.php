@extends('layouts.app')
@section('title') {{ __('custom.newTopic') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.newTopic') }}</span>
                    </div>

                    <div class="card-body">

                        <form autocomplete="off" method="POST" action="{{ route('topics.store') }}"
                              enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <input type="hidden" name="type_id" value="{{ $type }}"/>

                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="type">{{ __('custom.type') }}</label>
                                        <select name="type_id" id="type" class="form-control">
                                            @foreach(\App\Topic::$types as $_name => $_id)
                                                <option
                                                    value="{{ $_id }}"
                                                    @if($_id == old('type_id', $type)) selected @endif
                                                >
                                                    {{ __('custom.'.$_name) }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @if($errors->has('type_id'))
                                            <div class="error">{{ $errors->first('type_id') }}</div>
                                        @endif
                                    </div>
                                </div>


                                <div class="col-lg-12">
                                    <div class="form-group">
                                        <label for="title">{{ __('custom.title') }}</label>
                                        <input name="title" required="required" id="title" type="text"
                                               class="form-control" value="{{ old('title') }}">
                                        @if($errors->has('title'))
                                            <div class="error">{{ $errors->first('title') }}</div>
                                        @endif
                                    </div>
                                </div>

                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="form-group">

                                        <label for="users" id="usersLabel"
                                               class="@if(old('type_id', $type) == \App\Topic::$types['task']) d-none @endif">{{ __('custom.participants') }}</label>
                                        <label for="users" id="participantsLabel"
                                               class="@if(old('type_id', $type) != \App\Topic::$types['task']) d-none @endif">{{ __('custom.responsible') }}</label>

                                        <select multiple="multiple" class="form-control" id="users" name="users[]">
                                            @foreach($users as $_user)
                                                <option
                                                    @if(!empty(old('users')) && in_array($_user->id, old('users'))) selected
                                                    @endif value="{{ $_user->id }}">{{ $_user->name }}</option>
                                            @endforeach
                                        </select>
                                        @if($errors->has('users'))
                                            <div class="error">{{ $errors->first('users') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div id="task-rows"
                                 class="@if(old('type_id', $type) != \App\Topic::$types['task']) d-none @endif">
                                <div class="form-group">
                                    <label for="description">{{ __('custom.description') }}</label>
                                    <textarea class="form-control" id="description" name="description">
                                    {{ old('description') }}
                                </textarea>
                                    @if($errors->has('description'))
                                        <div class="error">{{ $errors->first('description') }}</div>
                                    @endif
                                </div>
                                <div class="form-group">
                                    <label for="priority">{{ __('custom.priority') }}</label>
                                    <select name="priority_id" id="priority" class="form-control">
                                        @foreach(\App\Topic::$priorities as $_name => $_id)
                                            <option
                                                value="{{ $_id }}"
                                                @if($_id == old('priority_id', \App\Topic::$priorities['medium'])) selected @endif
                                            >
                                                {{ __('custom.'.$_name) }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @if($errors->has('priority_id'))
                                        <div class="error">{{ $errors->first('priority_id') }}</div>
                                    @endif
                                </div>


                                <div class="form-group">
                                    <label for="deadline">{{ __('custom.deadline') }}</label>
                                    <input name="deadline" class="form-control" id="deadline" type="text"
                                           value="{{ old('deadline') }}">
                                    @if($errors->has('deadline'))
                                        <div class="error">{{ $errors->first('deadline') }}</div>
                                    @endif
                                </div>
                            </div>

                            <div class="form-group">
                                <input id="uploads" type="file" name="uploads[]" multiple>
                                @if($errors->has('uploads'))
                                    <div class="error">{{ $errors->first('uploads') }}</div>
                                @endif
                            </div>

                            <hr/>

                            <a id="category-picker" class="btn btn-default" href="#">
                                {{ __('custom.assignCategories') }}
                            </a>

                            <input id="categories" type="hidden" name="categories" value=""/>

                            <input value="{{ __('custom.submit') }}" type="submit" class="btn btn-primary">

                        </form>


                    </div>
                </div>
            </div>
        </div>

        @include('partials/categories_modal', ['header' => __('custom.selectCategories'), 'action' => __('custom.save'), 'categories' => $categories, 'selected_categories' => $categories, 'cascade' => 1])
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function () {

            initializeTextEditor(
                '#description',
                '{{ route('image.upload') }}',
                '{{ csrf_token() }}',
                {{ $viewData['themeName'] == 'default-dark' }}
            );

            $('#deadline').datepicker({
                minDate: 0,
                dateFormat: 'dd.mm.yy.'
            });

            let priority_picker = $('#priority').select2({minimumResultsForSearch: -1});
            let type_picker = $('#type').select2({minimumResultsForSearch: -1});
            let user_picker = $('#users').select2({closeOnSelect: false});

            type_picker.on('change', function (e) {
                // if task
                if (e.target.value == 2) {
                    $('#task-rows').removeClass('d-none');
                    // need to re-initialize due to width issues with select2
                    priority_picker.select2('destroy').select2({minimumResultsForSearch: -1});
                    user_picker.select2('destroy').select2({closeOnSelect: false});
                    // change label for users
                    $('#usersLabel').addClass('d-none');
                    $('#participantsLabel').removeClass('d-none');
                } else {
                    $('#task-rows').addClass('d-none');
                    $('#usersLabel').removeClass('d-none');
                    $('#participantsLabel').addClass('d-none');
                }
            });

            // otherwise images escape containers
            $(".comment-content table").wrap("<div class='table-responsive'></div>");
            $("img").addClass("img-responsive");
            $("img").css("max-width", "100%");
            $("img").css("height", "auto");
            $("video").css("max-width", "100%");
            $("video").css("height", "auto");

            // categories
            $('#submit-categories').on('click', function (e) {
                let categories = $("#category-tree").jstree("get_checked", null, true);

                $('#categories').val(categories);
                $('#categoriesModal').modal('hide');

            });

        });
    </script>
@endpush
