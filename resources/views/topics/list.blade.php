@extends('layouts.app')
@section('title') {{ $title }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ $title }}</span>
                        <a id="newTopic" class="btn btn-primary float-right" href="{{ $newTopicLink }}">+ {{ $newTopicLinkText }}</a>
                    </div>

                    <div class="card-body fixed-height-body">
                        <div class="row">
                            <div class="col-lg-6 mb-2 mb-lg-0">
                                <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                    <li class="nav-item">
                                        <a data-id="active" class="tab-switch nav-link active" id="pills-active-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-active" aria-selected="true">
                                            {{ __('custom.activeTopics') }}
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a data-id="completed" class="tab-switch nav-link" id="pills-completed-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-active" aria-selected="true">
                                            {{ __('custom.completedTopics') }}
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a data-id="all" class="tab-switch nav-link" id="pills-all-tab" data-toggle="pill" href="#" role="tab" aria-controls="pills-all" aria-selected="false">
                                            {{ __('custom.allTopics') }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-lg-4 pt-2 mb-2 mb-lg-0">
                                <div class="form-check">
                                    <input class="form-check-input" autocomplete='off' type="checkbox" value="" id="showTasks">
                                    <label class="form-check-label" for="showTasks">
                                        {{ __('custom.showTasks') }}
                                        @if(!empty($viewData['activeTasksCount']))
                                            <span class="badge badge-pill badge-danger task-count-badge">
                                                {{ $viewData['activeTasksCount'] }}
                                            </span>
                                        @endif
                                    </label>
                                </div>
                            </div>
                            <div class="col-lg-2 mb-2 mb-lg-0">
                               <a id="category-picker" class="btn btn-default form-control" href="#">
                                   {{ __('custom.selectCategory') }}
                               </a>
                                <div id="categories-selected-div" class="text-center d-none">
                                    <small>{{ __('custom.categoriesSelectedCount') }}: <span id="selected-categories-count">0</span>
                                        <a class="ml-1" href="#" id="undo-categories">{{ __('custom.undo') }}</a>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <hr/>

                        <div id="show-only-my-tasks" class="form-check mt-2 mb-3 d-none">
                            <input autocomplete="off" checked class="form-check-input" type="checkbox" value="" id="showOnlyMine">
                            <label class="form-check-label" for="showOnlyMine">
                                {{ __('custom.showOnlyMyTasks') }}
                            </label>
                        </div>

                        <div id="table-div">
                            <table class="table table-borderless table-striped" id="topics-table">
                                <thead>
                                <tr>
                                    <th>id</th>
                                    <th>{{ __( 'custom.topic') }}</th>
                                    <th>{{ __( 'custom.type') }}</th>
                                    <th>{{ __( 'custom.priority') }}</th>
                                    <th>{{ __( 'custom.status') }}</th>
                                </tr>
                                </thead>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>


        @include('partials/categories_modal', ['header' => __('custom.selectCategory'), 'action' => __('custom.filter'), 'categories' => $categories, 'selected_categories' => $categories])

        <div id="category-topics-modal" class="modal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="category-topics-header"></h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="category-topics-table-div" style="padding-top:1px;">
                                    <table class="table table-borderless table-striped" id="category-topics-table">
                                        <thead>
                                        <tr>
                                            <th>id</th>
                                            <th>{{ __( 'custom.topic') }}</th>
                                            <th>{{ __( 'custom.type') }}</th>
                                            <th>{{ __( 'custom.priority') }}</th>
                                            <th>{{ __( 'custom.status') }}</th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(function() {

            let pageLength = 10;

            let status = 'active';

            let categories = [];

            let showTasks = 0;

            let showOnlyMine = 1;

            $('#showOnlyMine').on('change', function(e){
                showOnlyMine = this.checked ? 1 : 0;
                table.clear().destroy();
                table = initDataTable(status);
            });

            $('#showTasks').on('change', function(e){
                showTasks = this.checked ? 1 : 0;

                if(showTasks){
                    $('#show-only-my-tasks').removeClass('d-none');

                    // avoid triggering click event, to not reinitialize table
                    $('#showOnlyMine').prop('checked', true);
                    showOnlyMine = 1;
                }
                else{
                    $('#show-only-my-tasks').addClass('d-none');
                }

                table.clear().destroy();
                table = initDataTable(status);

            });

            function handleSelectedCategoriesDiv()
            {
                let count = $("#category-tree").jstree("get_checked",null,true).length;

                $('#selected-categories-count').html(count);

                if(count){
                    $('#categories-selected-div').removeClass('d-none');
                }
                else{
                    $('#categories-selected-div').addClass('d-none');
                }
            }

            $('#undo-categories').on('click', function(e){
                $("#category-tree").jstree().deselect_all(true);
                categories = [];
                table.clear().destroy();
                table = initDataTable(status);
                handleSelectedCategoriesDiv();
                $('#categoriesModal').modal('hide');
            });

            let table = initDataTable(status);

            table.on( 'length.dt', function ( e, settings, len ) {
                pageLength = len;
            } );

            $('.tab-switch').on('click', function(e){
                table.clear().destroy();
                status = $(e.target).data('id');
                table = initDataTable(status);
            });

            $('#submit-categories').on('click', function(e){
                categories = $("#category-tree").jstree("get_checked",null,true);
                table.clear().destroy();
                table = initDataTable(status);
                handleSelectedCategoriesDiv();
                $('#categoriesModal').modal('hide');
            });

            function initDataTable(status)
            {
                let route = '{!! route('topics.list', ['status' => 'all']) !!}';

                if(status === 'active'){
                    route = '{!! route('topics.list', ['status' => 'active']) !!}';
                }
                else if(status === 'completed'){
                    route = '{!! route('topics.list', ['status' => 'completed']) !!}';
                }
                else if(status === 'own'){
                    route = '{!! route('topics.list', ['status' => 'own']) !!}';
                }

                let ajax_route = route+"&categories="+categories+"&show_tasks="+showTasks;

                if($('#showOnlyMine').length) {
                    ajax_route+="&show_only_mine="+showOnlyMine;
                }

                $('#table-div').hide();

                return $('#topics-table').DataTable({
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    responsive: {
                        details: false
                    },
                    pageLength: pageLength,
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: ajax_route,
                        cache: true
                    },
                    initComplete: function() {
                        $('#table-div').fadeIn(100);
                    },
                    columns: [
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            render: function(data, type, row) {
                                return !isMobile() ? (
                                    '<a class="dt-link" href="' + row['show_link'] + '" ' +
                                    'data-toggle="popover" data-content="' + row['categories'] + '">' + data + '</a>'
                                ) : '<a class="dt-link" href="' + row['show_link'] + '">' + data + '</a>'
                            }
                        },
                        {
                            data: 'type_id', name: 'type_id',
                        },
                        {
                            data: 'priority',
                            type: 'html',
                            name: 'priority_id',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['priority']+'</span>';
                            }
                        },
                        {
                            data: 'status_id',
                            sortable: false,
                            type: 'html',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['status']+'</span>';
                            }
                        },
                    ],
                    columnDefs: [
                        {
                            targets: [ 0],
                            visible: false,
                        },
                        {
                            targets: [2,3,4],
                            visible: !isMobile()
                        },
                        {
                            targets: [ 0, 2, 3, 4],
                            searchable: false
                        },
                    ],
                    drawCallback: function() {

                        $('[data-toggle="popover"]').popover({
                            trigger: 'manual',
                            placement: 'right',
                            html: true
                        }).on('mouseenter', function () {
                            let _this = this;
                            clearTimeout($(_this).data('timeoutId')); // Clear any existing timeout

                            // Set a timeout to show the popover after 200ms
                            let hoverTimeout = setTimeout(function () {
                                $(_this).popover('show');

                                // Ensure popover stays open when hovered
                                $('.popover').on('mouseenter', function () {
                                    clearTimeout($(_this).data('timeoutId'));
                                }).on('mouseleave', function () {
                                    $(_this).popover('hide');
                                });
                            }, 500);
                            $(_this).data('hoverTimeout', hoverTimeout); // Store the timeout ID for hover

                        }).on('mouseleave', function () {
                            let _this = this;
                            clearTimeout($(_this).data('hoverTimeout')); // Clear the hover timeout if mouse leaves early

                            let timeoutId = setTimeout(function () {
                                if (!$('.popover:hover').length) {
                                    $(_this).popover('hide');
                                }
                            }, 100); // Slightly increased timeout for better UX
                            $(_this).data('timeoutId', timeoutId); // Store the timeout ID
                        });

                    }
                });
            }

            function initTopicCategoriesDataTable(category_id)
            {
                let route = '{!! route('topics.list', ['status' => 'active']) !!}';

                let ajax_route = route+"&categories="+category_id;

                $('#category-topics-table-div').hide();

                return $('#category-topics-table').DataTable({
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    responsive: {
                        details: false

                    },
                    pageLength: 10,
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: ajax_route,
                        cache: true
                    },
                    initComplete: function() {
                        $('#category-topics-table-div').fadeIn(100);
                    },
                    columns: [
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            render: function(data, type, row) {
                                return '<a class="dt-link" href="'+ row['show_link'] +'">' + data + '</a>';
                            }
                        },
                        {
                            data: 'type_id', name: 'type_id',
                        },
                        {
                            data: 'priority',
                            type: 'html',
                            name: 'priority_id',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['priority']+'</span>';
                            }
                        },
                        {
                            data: 'status_id',
                            sortable: false,
                            type: 'html',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['status']+'</span>';
                            }
                        },
                    ],
                    columnDefs: [
                        {
                            targets: [ 0],
                            visible: false,
                        },
                        {
                            targets: [2,3,4],
                            visible: !isMobile()
                        },
                        {
                            targets: [ 0, 2, 3, 4],
                            searchable: false
                        },
                    ],
                });
            }

            // delegate the category badge click event to avoid re-binding
            $(document).on('click', '.category-badge', function(e) {
                // hide any visible popovers
                $('[data-toggle="popover"]').popover('hide');

                let category_id = $(this).data('id');

                if(category_id) {
                    let category_name = $(this).html();

                    $('#category-topics-header').html(
                        '<span class="badge badge-primary category-badge">'
                        + category_name +
                        '</span>');

                    initTopicCategoriesDataTable(category_id);
                    $('#category-topics-modal').modal();
                }
            });

            // destroy the DataTable when the modal is hidden
            $('#category-topics-modal').on('hidden.bs.modal', function () {
                $('#category-topics-table').DataTable().destroy();
            });

        });
    </script>
@endpush
