@foreach($comments as $i => $comment)
    <div data-id="{!! $comment->id !!}" class="comment-container bordered-div mb-4 @if($comment->hasDocumentation()) alert-warning @endif"
         @if(!$comment->hasDocumentation() && $viewData['themeName'] != 'default-dark') style="background: #fafafa" @endif>

        <span class="font-weight-bold">
           <img class="user-avatar" src="{{ Avatar::create($comment->user->name)->toBase64() }}"/>
            <a class="comment-link" href="{{ route('comments.show', $comment) }}">
                {{ $comment->user->name }} @ {{ $comment->updated_at }}
            </a>
        </span>

        <hr/>

        @if($comment->referencedComment)

            <div class="bordered-div mb-4"
                 style="background: @if($viewData['themeName'] == 'default-dark') #444444 @else #ffffff @endif">
                <img class="user-avatar" src="{{ Avatar::create($comment->referencedComment->user->name)->toBase64() }}"/>
                <a class="comment-link" href="{{ route('comments.show', $comment->referencedComment) }}">
                                           <span class="font-weight-bold">
                                                {{ $comment->referencedComment->user->name }} @ {{ $comment->referencedComment->updated_at }}
                                            </span>
                </a>

                <hr/>

                @if(!empty(strip_tags($comment->referencedComment->comment, '<img>')))
                    <div class="comment-content mb-4">
                        {!! $comment->referencedComment->comment !!}
                    </div>
                @endif

                @if($comment->referencedComment->hasDocumentation())
                    <div>
                        <ul>
                            <li>
                                <label class="font-weight-bold">
                                    {{ __('custom.documentation') }}:
                                    <a target="_blank"
                                       href="{{ route('documentation.show', [$comment->referencedComment->documentation->id]) }}">
                                        {{ $comment->referencedComment->documentation->title }}
                                    </a>
                                </label>
                            </li>
                        </ul>
                    </div>
                @endif

                @if(count($comment->referencedComment->uploads))
                    <ul>
                        @foreach($comment->referencedComment->uploads as $_upload)
                            <li>
                                <a target="_blank"
                                   href="{{ route('resource.download', $_upload->id) }}">
                                    {{ $_upload->name }}
                                </a>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </div>
        @endif

        @if(!empty(strip_tags($comment->comment, '<img>')))
            <div class="comment-content mb-4">
                {!! $comment->comment !!}
            </div>
        @endif
        @if($comment->hasDocumentation())
            <div>
                <ul>
                    <li>
                        <label class="font-weight-bold">
                            {{ __('custom.documentation') }}:
                            <a target="_blank" href="{{ route('documentation.show', [$comment->documentation->id]) }}">
                                {{ $comment->documentation->title }}
                            </a>
                        </label>
                    </li>
                </ul>
            </div>
        @endif
        @if(count($comment->uploads))
            <ul>
                @foreach($comment->uploads as $_upload)
                    <li>
                        <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                            {{ $_upload->name }}
                        </a>
                    </li>
                @endforeach
            </ul>
        @endif
        @if($comment->belongsToUser(Auth::user()))
            <hr/>
            <a href="{{ route('comments.edit', [$comment->id]) }}" class="btn btn-link">{{ __('custom.edit') }}</a>
            <a data-confirmation="{{ __('custom.pleaseConfirm') }}" href="{{ route('comments.destroy', [$comment->id]) }}"
               class="pleaseConfirm btn btn-link">{{ __('custom.delete') }}</a>
        @endif
        <button data-toggle="tooltip" data-placement="right" title="{{ $comment->getUserLikesNames() }}" type="button"
                class="btn {{ in_array(Auth::id(), $comment->getUserLikesIds()) ? 'btn-primary like-comment' : 'btn-default like-comment' }}"
                data-id="{{ $comment->id }}">
            <span class="like-count">{{ count($comment->getUserLikesIds()) }}</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                 class="bi bi-hand-thumbs-up-fill" viewBox="0 0 16 16">
                <path
                    d="M6.956 1.745C7.021.81 7.908.087 8.864.325l.261.066c.463.116.874.456 1.012.965.22.816.533 2.511.062 4.51a9.84 9.84 0 0 1 .443-.051c.713-.065 1.669-.072 2.516.21.518.173.994.681 1.2 1.273.184.532.16 1.162-.234 1.733.***************.138.363.077.27.113.567.113.856 0 .289-.036.586-.113.856-.039.135-.09.273-.16.404.169.387.107.819-.003 1.148a3.163 3.163 0 0 1-.488.901c.054.152.076.312.076.465 0 .305-.089.625-.253.912C13.1 15.522 12.437 16 11.5 16H8c-.605 0-1.07-.081-1.466-.218a4.82 4.82 0 0 1-.97-.484l-.048-.03c-.504-.307-.999-.609-2.068-.722C2.682 14.464 2 13.846 2 13V9c0-.85.685-1.432 1.357-1.615.849-.232 1.574-.787 2.132-1.41.56-.627.914-1.28 1.039-1.639.199-.575.356-1.539.428-2.59z"></path>
            </svg>
        </button>
    </div>
@endforeach
