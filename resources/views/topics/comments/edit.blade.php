@extends('layouts.app')
@section('title') {{ __('custom.editTopicComment') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.editTopicComment') }}</span>
                    </div>

                    <div class="card-body">

                        <form autocomplete="off" method="POST" action="{{ route('comments.update', [$comment->id]) }}" enctype="multipart/form-data">
                            @csrf
                            {{method_field('PUT')}}
                            <div class="form-group">
                                <span class="font-weight-bold">{{ __('custom.topic') }}: </span>
                                <a href="{{ route('topics.show', [$comment->topic->id]) }}">{{ $comment->topic->title }}</a>
                            </div>

                            @if($comment->referencedComment)
                                <div id="comment-reference">
                                    <input type="hidden" name="ref_id" value="{{ $comment->referencedComment->id }}">
                                    <span>{{ __('custom.replyReference') }}</span>
                                    <ul>
                                        <li>
                                            <a href="{{ route('comments.show', $comment->referencedComment) }}">
                                                {{ $comment->referencedComment->user->name }} @ {{ $comment->referencedComment->updated_at }}
                                            </a>
                                            <a class="btn btn-xs btn-primary remove-comment-reference" href="#">x</a>
                                        </li>
                                    </ul>
                                </div>
                            @endif

                            <div class="form-group">
                                <label for="description">{{ __('custom.comment') }}</label>
                                <textarea class="form-control" id="comment" name="comment">
                                    {{ old('comment', $comment->comment) }}
                                </textarea>
                                @if($errors->has('comment'))
                                    <div class="error">{{ $errors->first('comment') }}</div>
                                @endif
                            </div>

                            <div class="form-group">
                                <label for="documentation-picker">{{ __('custom.documentation') }}</label>

                                <small id="delete-documentation-text" class="@if(!$comment->hasDocumentation()) d-none @endif">
                                    <a href="#" id="delete-documentation">
                                        {{ __('custom.remove') }}
                                    </a>
                                </small>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <select name="documentation_id" class="form-control" id="documentation-picker">
                                                @if($comment->hasDocumentation())
                                                    <option selected value="{{ $comment->documentation_id }}">{{ $comment->documentation->title }}</option>
                                                @endif
                                            </select>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            @if(count($comment->uploads))
                                <span>{{ __('custom.files') }}</span>
                                <ul>
                                    @foreach($comment->uploads as $_upload)
                                        <li class="existing_upload">
                                            <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                {{ $_upload->name }}
                                            </a>
                                            <input type="hidden" name="existing_uploads[]" value="{{ $_upload->id }}">
                                            <a class="btn btn-xs btn-primary delete-upload" href="#">x</a>
                                        </li>

                                    @endforeach
                                </ul>
                            @endif

                            <div class="form-group">
                                <input id="uploads" type="file" name="uploads[]" multiple>
                                @if($errors->has('uploads'))
                                    <div class="error">{{ $errors->first('uploads') }}</div>
                                @endif
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input checked name="notify_users" type="checkbox" class="form-check-input" id="notify_users">
                                    <label class="form-check-label" for="notify_users">Obavijesti sudionike predmeta o promjenama</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <input value="{{ __('custom.submit') }}" type="submit" class="btn btn-primary btn-block">
                            </div>

                        </form>


                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {

            initializeTextEditor(
                '#comment',
                '{{ route('image.upload') }}',
                '{{ csrf_token() }}',
                {{ $viewData['themeName'] == 'default-dark' }}
            );


            $('.delete-upload').on('click', function(e){
                e.preventDefault();
                let question = "{{ __('custom.pleaseConfirm') }}";
                if (window.confirm(question)) {
                    $(this).closest('.existing_upload').remove();
                }
            });

            $('#delete-documentation').on('click', function(e){
                e.preventDefault();
                let question = "{{ __('custom.pleaseConfirm') }}";
                if (window.confirm(question)) {
                    $('#delete-documentation-text').addClass('d-none');
                    $('#documentation-picker').empty();
                }
            });

            $('.remove-comment-reference').on('click', function(e){
                e.preventDefault();
                let question = "{{ __('custom.pleaseConfirm') }}";
                if (window.confirm(question)) {
                    $('#comment-reference').remove();
                }

            });

            $('#documentation-picker').select2({
                placeholder: '{{ __('custom.search') }}...',
                minimumInputLength: 3,
                ajax: {
                    url: '{{ route('documentation.search') }}',
                    dataType: 'json',
                    delay: 200,
                }
            });

            $('#documentation-picker').on('select2:select', function (e) {
                $('#delete-documentation-text').removeClass('d-none');
            });

            // otherwise images escape containers
            $(".comment-content table").wrap( "<div class='table-responsive'></div>" );
            $("img").addClass("img-responsive");
            $("img").css("max-width", "100%");
            $("img").css("height", "auto");
            $("video").css("max-width", "100%");
            $("video").css("height", "auto");
        });
    </script>
@endpush
