@extends('layouts.app')
@section('title')
    {{ __('custom.comment') }} -
@endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.comment') }}</span>
                    </div>

                    <div class="card-body">

                        <div class="form-group">
                            <span class="font-weight-bold">{{ __('custom.topic') }}: </span>
                            <a href="{{ route('topics.show', [$comment->topic->id]) }}">{{ $comment->topic->title }}</a>
                        </div>

                        @if($has_next_comments)
                            <div class="text-lg-right text-center">
                                <a id="load-next-comments" href="javascript:void(0);" class="btn btn-link">{{ __('custom.loadNextComment') }}</a>
                            </div>
                            <div id="next-comments"></div>
                        @endif

                        @include('topics.comments.partials.comments', ['comments' => [$comment]])

                        @if($has_previous_comments)
                            <div id="previous-comments"></div>
                            <div class="text-lg-right text-center">
                                <a id="load-previous-comments" href="javascript:void(0);" class="btn btn-link">{{ __('custom.loadPreviousComment') }}</a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function () {

            // load next comments
            $('#load-next-comments').click(function () {

                let firstComment = $('div.comment-container').first().data('id');

                $.ajax({
                    url: "/comments/" + firstComment + "/next",
                    data: {
                        limit: 1
                    },
                    success: function (data) {
                        $('#next-comments').prepend(data.html).show();

                        // if no more comments
                        if(!data.more) {
                            $('#load-next-comments').hide();
                        }

                        bindImagesToContainers();
                    },
                    error: function () {
                        $(this).hide();
                    }
                })
            });

            // load previous comments
            $('#load-previous-comments').click(function () {

                let lastComment = $('div.comment-container').last().data('id');

                $.ajax({
                    url: "/comments/" + lastComment + "/previous",
                    data: {
                        limit: 1
                    },
                    success: function (data) {
                        $('#previous-comments').append(data.html).show();

                        // if no more comments
                        if(!data.more) {
                            $('#load-previous-comments').hide();
                        }

                        bindImagesToContainers();
                    }
                })
            });


            $('[data-toggle="tooltip"]').tooltip();

            $('.like-comment').on('click', function (e) {

                $(this).tooltip('hide');

                let element = $(this);
                let commentId = $(this).data('id');

                // like
                if ($(this).hasClass('btn-default')) {

                    $.post("{{ route('comments.like') }}",
                        {
                            'id': commentId,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function (data) {
                            // update counter, like names
                            element.find('.like-count').html(data['like_count']);
                            element.attr('data-original-title', data['like_names']);

                            // toggle color
                            element.removeClass('btn-default').addClass('btn-primary');
                        }
                    );

                } else { // unlike

                    $.post("{{ route('comments.unlike') }}",
                        {
                            'id': commentId,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function (data) {
                            // update counter, like names
                            element.find('.like-count').html(data['like_count']);
                            element.attr('data-original-title', data['like_names']);

                            // toggle color
                            element.removeClass('btn-primary').addClass('btn-default');
                        }
                    );
                }

            });

            function bindImagesToContainers() {
                $(".comment-content table").wrap("<div class='table-responsive'></div>");
                $("img, video").addClass("img-responsive").css({
                    "max-width": "100%",
                    "height": "auto"
                });
            }

            bindImagesToContainers();

        });
    </script>
@endpush
