@extends('layouts.app')
@section('title') {{ $topic->title }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row">
                            <div class="col-lg-10">
                                {{ $topic->title }}
                            </div>
                            <div class="col-lg-2 pt-2 pt-lg-0">
                                <div class="dropdown show">
                                    <a class="btn btn-block btn-primary dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {{ __('custom.chooseAction') }}
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                                        @if($topic->user_id === auth()->id())
                                            <a class="dropdown-item" href="{!! route('topics.edit', [$topic->id]) !!}">{{ __('custom.editTopic') }}</a>
                                            @if($topic->reminder)
                                                <a class="dropdown-item" href="{!! route('topics.reminders.edit', [$topic->reminder->id]) !!}">{{ __('custom.editReminder') }}</a>
                                            @else
                                                <a class="dropdown-item" href="{!! route('topics.reminders.create', [$topic->id]) !!}">{{ __('custom.addReminder') }}</a>
                                            @endif
                                        @endif
                                        @if(Auth::user()->isAdmin())
                                            <button style="cursor: pointer;" id="edit-categories" class="dropdown-item">{{ __('custom.editCategories') }}</button>
                                        @endif
                                        @if(Auth::user()->canLeaveTopic($topic))
                                            <a class="dropdown-item pleaseConfirm" data-confirmation="{{ __('custom.pleaseConfirm') }}" href="{{ route('topics.leave', [$topic->id]) }}">{{ __('custom.leaveTopic') }}</a>
                                            @endif
                                        @if(($topic->user_id === auth()->id()) && !$topic->isCompleted())
                                            <a class="dropdown-item pleaseConfirm" data-confirmation="{{ __('custom.pleaseConfirm') }}" href="{{ route('topics.complete', [$topic->id]) }}">{{ __('custom.closeTopic') }}</a>
                                        @endif
                                        @if(Auth::user()->belongsToTopic($topic) && $topic->isCompleted())
                                            <a class="dropdown-item pleaseConfirm" data-confirmation="{{ __('custom.pleaseConfirm') }}" href="{{ route('topics.reopen', [$topic->id]) }}">{{ __('custom.reopenTopic') }}</a>
                                        @endif
                                        @if($topic->user_id === auth()->id())
                                            <a data-confirmation="{{ __('custom.pleaseConfirm') }}" class="pleaseConfirm dropdown-item" href="{!! route('topics.destroy', [$topic->id]) !!}">{{ __('custom.destroyTopic') }}</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                @foreach($topic->categories as $_category)
                                    <span class="badge badge-primary category-badge" data-id="{{ $_category->id }}">
                                        #{{ $_category->title }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        @if($topic->reminder && Auth::user()->canSeeTopicReminder($topic->reminder))
                            <div class="alert alert-warning">
                                <strong>{{__('custom.reminder')}}:</strong> {{ $topic->reminder->message }} @if(!$topic->reminder->isActive())<small>[{{__('custom.paused')}}]</small> @endif
                            </div>
                        @endif
                        <div class="table-responsive">
                            <table class="table table-striped table-hover table-bordered">
                                <thead>
                                <tr>
                                    <td>{{ __('custom.createdAt') }}</td>
                                    <td> {{ __('custom.type') }} </td>
                                    @if($topic->isTask())
                                        <td>{{ __('custom.deadline') }}</td>
                                    @endif
                                    <td>{{ __('custom.creator') }}</td>
                                    @if($topic->isTask())
                                        <td>{{ __('custom.responsible') }}</td>
                                    @else
                                        <td>{{ __('custom.participants') }}</td>
                                    @endif
                                    @if($topic->isTask())
                                        <td>{{ __('custom.priority') }}</td>
                                    @endif
                                    <td>{{ __('custom.status') }}</td>
                                </tr>
                                </thead>
                                <tbody>
                                <tr>
                                    <td>{{ $topic->created_at }}</td>
                                    <td>{{ $topic->isTask() ? __('custom.task') : __('custom.topic') }}</td>
                                    @if($topic->isTask())
                                        <td class="@if($topic->isActive() && !empty($topic->deadline) && \Carbon\Carbon::create($topic->deadline)->isPast()) alert-danger @endif">{{ $topic->deadline ?: '' }}</td>
                                    @endif
                                    <td>{{ $topic->creator->name ?: '' }}</td>
                                    <td>{{ implode(', ', array_map(function($user) { return $user['name']; }, $topic->users->toArray())) }}</td>
                                    @if($topic->isTask())
                                        <td>
                                            @if($topic->isActive() && $topic->isUrgent())
                                                <span class="badge badge-danger">{{ __('custom.'.$topic->getPriority()) }}</span>
                                            @else
                                                {{ __('custom.'.$topic->getPriority()) }}
                                            @endif
                                        </td>
                                    @endif
                                    <td>
                                        @if($topic->isActive())
                                            <span class="badge badge-success">{{ __('custom.'.$topic->getStatus()) }}</span>
                                        @elseif($topic->isCompleted())
                                            <span class="badge badge-secondary">{{ __('custom.'.$topic->getStatus()) }}</span>
                                        @else
                                        {{ __('custom.'.$topic->getStatus()) }}
                                        @endif
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>

                        @if(($topic->isTask() && $topic->description) || count($topic->uploads))
                            <div class="topic-description">
                                {!! $topic->description ?: null !!}
                                @if(count($topic->uploads))
                                    <div class="mt-2">
                                        <span class="font-weight-bold">{{ __('custom.files') }}</span>
                                        <ul>
                                            @foreach($topic->uploads as $_upload)
                                                <li>
                                                    <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                        {{ $_upload->name }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                            </div>
                        @endif

                    </div>
                </div>
                <div class="card mt-4">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-12">
                                    <h4>
                                        {{ __('custom.discussion') }}
                                        @if($topic->isActive() && Auth::user()->belongsToTopic($topic))
                                            <span class="float-right">
                                               <a class="btn btn-default" data-toggle="collapse" id="add-comment" href="#commentBox" role="button" aria-expanded="false" aria-controls="commentBox">
                                                + {{ __('custom.addComment') }}
                                               </a>
                                            </span>
                                        @endif
                                    </h4>
                                </div>
                            </div>
                            <div class="collapse {{ !$errors->isEmpty() ? "show" :  "" }}" id="commentBox">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mt-3 mb-3">
                                            <div id="ref-div" style="padding-bottom: 30px;" class="@if(empty(old('ref_id'))) d-none @endif">
                                                @if(old('ref_id'))
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-reply-fill" style="margin-bottom: 3px;" viewBox="0 0 16 16">
                                                        <path d="M5.921 11.9 1.353 8.62a.719.719 0 0 1 0-1.238L5.921 4.1A.716.716 0 0 1 7 4.719V6c1.5 0 6 0 7 8-2.5-4.5-7-4-7-4v1.281c0 .56-.606.898-1.079.62z"/>
                                                    </svg>

                                                    <a href="" data-id="{{ old('ref_id') }}" class="comment-reference-link">{{ old('ref_header') }}</a>  <a class="btn btn-xs btn-primary remove-comment-reference" href="#">x</a>
                                                @endif
                                            </div>
                                            <nav id="topic-nav">
                                                <div class="nav nav-tabs" id="nav-tab" role="tablist">
                                                    <a class="nav-item nav-link @if(!old('new_documentation') && !old('existing_documentation')) active @endif" id="nav-comment-tab" data-toggle="tab" href="#nav-comment" role="tab" aria-controls="nav-comment" aria-selected="true">
                                                        {{ __('custom.comment') }}
                                                    </a>
                                                    <a class="nav-item nav-link @if(old('new_documentation') || old('existing_documentation')) active @endif" id="nav-documentation-tab" data-toggle="tab" href="#nav-documentation" role="tab" aria-controls="nav-documentation" aria-selected="false">
                                                        {{ __('custom.documentation') }}
                                                    </a>
                                                </div>
                                            </nav>
                                            <div class="tab-content" id="nav-tabContent">
                                                <div class="tab-pane fade @if(!old('new_documentation') && !old('existing_documentation')) show active @endif" id="nav-comment" role="tabpanel" aria-labelledby="nav-comment-tab">
                                                    <form method="POST" action="{{ route('comments.create', [$topic->id]) }}" enctype="multipart/form-data">
                                                        @csrf

                                                        <input type="hidden" class="ref-id" name="ref_id" value="{{old('ref_id')}}">
                                                        <input type="hidden" class="ref-header" name="ref_header" value="{{old('ref_header')}}">

                                                        <div class="form-group">
                                                            <textarea id="comment" name="comment" class="form-control"></textarea>
                                                            @if($errors->has('comment'))
                                                                <div class="error">{{ $errors->first('comment') }}</div>
                                                            @endif
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="file" multiple name="uploads[]">
                                                        </div>

                                                        <div class="form-group">
                                                            <div class="form-check">
                                                                <input checked name="notify_users" type="checkbox" class="form-check-input" id="notify_users_1">
                                                                <label class="form-check-label" for="notify_users_1">Obavijesti sudionike predmeta</label>
                                                            </div>
                                                        </div>

                                                        {{--Anchor for filtering comments--}}
                                                        <span id="comments"></span>

                                                        <div class="form-group">
                                                            <input type="submit" value="{{ __('custom.postComment') }}" class="btn btn-primary btn-block">
                                                        </div>
                                                    </form>
                                                </div>
                                                <div class="tab-pane fade p-2  @if(old('new_documentation') || old('existing_documentation')) show active @endif" id="nav-documentation" role="tabpanel" aria-labelledby="nav-documentation-tab">
                                                    <form autocomplete="off" method="POST" action="{{ route('comments.documentation.create', [$topic->id]) }}" enctype="multipart/form-data">
                                                        @csrf

                                                        <input type="hidden" class="ref-id" name="ref_id" value="{{old('ref_id')}}">
                                                        <input type="hidden" class="ref-header" name="ref_header" value="{{old('ref_header')}}">

                                                        <div class="row">
                                                            <div class="col-lg-12">
                                                                <div class="form-group mt-2">
                                                                    <select style="width:100%;" id="documentation-new-picker" class="form-control" name="documentation_type">
                                                                        <option value="new" @if( (old('documentation_type') == null) || (old('documentation_type') == "new") ) selected @endif >{{ __('custom.newDocumentation') }}</option>
                                                                        <option value="existing" @if( old('documentation_type') == "existing" ) selected @endif>{{ __('custom.existingDocumentation') }}</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div id="newDocumentationFields" @if(old('documentation_type') == "existing") class="d-none" @endif>
                                                            <div class="form-group mb-4">
                                                                <label for="documentation-title">{{ __('custom.title') }}</label>
                                                                <input id="documentation-title" name="title" class="form-control" value="{{ old('title') }}">
                                                                @if($errors->has('title'))
                                                                    <div class="error">{{ $errors->first('title') }}</div>
                                                                @endif
                                                            </div>
                                                            <div class="form-group mt-2 mb-4">
                                                                <textarea id="content" name="content" class="form-control">{{ old('content') }}</textarea>
                                                                @if($errors->has('content'))
                                                                    <div class="error">{{ $errors->first('content') }}</div>
                                                                @endif
                                                            </div>

                                                            <div class="form-group">
                                                                <input type="file" multiple name="uploads[]">
                                                            </div>

                                                            <div class="form-group">
                                                                <div class="form-check">
                                                                    <input checked name="notify_users" type="checkbox" class="form-check-input" id="notify_users_2">
                                                                    <label class="form-check-label" for="notify_users_2">Obavijesti sudionike predmeta</label>
                                                                </div>
                                                            </div>

                                                            <div class="form-group">
                                                                <input name="new_documentation" type="button" value="{{ __('custom.postDocumentation') }}" class="btn btn-primary btn-block submit-documentation">
                                                            </div>
                                                        </div>
                                                        <div id="existingDocumentationFields" class="@if( (old('documentation_type') == null) || (old('documentation_type') == "new") ) d-none @endif mb-3">
                                                            <select style="width: 100%;" name="documentation_id" class="form-control" id="documentation-picker">

                                                            </select>
                                                            @if($errors->has('documentation_id'))
                                                                <div class="error">{{ $errors->first('documentation_id') }}</div>
                                                            @endif

                                                            <div class="form-group mt-3">
                                                                <input name="existing_documentation" type="button" value="{{ __('custom.postDocumentation') }}" class="btn btn-primary btn-block submit-documentation">
                                                            </div>
                                                        </div>
                                                        <div id="documentation-comment-modal" class="modal" tabindex="-1" role="dialog">
                                                            <div class="modal-dialog" role="document">
                                                                <div class="modal-content">
                                                                    <div class="modal-header">
                                                                        <h5 class="modal-title">Dodaj komentar?</h5>
                                                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                    </div>
                                                                    <div class="modal-body">
                                                                        <div class="row">
                                                                            <div class="col-lg-12">
                                                                                <textarea id="documentation-comment" name="documentation_comment" class="form-control">{{ old('documentation_comment') }}</textarea>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <input id="submit-documentation" name="" type="submit" value="{{ __('custom.submit') }}" class="btn btn-primary btn-block">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <hr/>
                            <form autocomplete="off" action="{{ route('topics.show', $topic->id) }}#comments" id="comment-filter-form">
                                <div class="row mt-2">
                                    <div class="col-lg-6 mb-2 mb-lg-0">
                                        <input class="form-control" id="filter-comments-query" name="filter_comments_query" value="{{ request('filter_comments_query') }}" type="text" placeholder="{{ __('custom.search') }}..."/>
                                    </div>
                                    <div class="col-lg-2 mb-2 mb-lg-0">
                                        <select class="form-control" id="comment-filter" name="filter_comments">
                                            <option value="all" @if(!request('filter_comments') || (request('filter_comments') == 'all')) selected @endif>
                                                {{ __('custom.filterAll') }}
                                            </option>
                                            <option value="documentation" @if((request('filter_comments') == 'documentation')) selected @endif>
                                                {{ __('custom.documentation') }}
                                            </option>
                                            <option value="my" @if((request('filter_comments') == 'my')) selected @endif>
                                                {{ __('custom.myCommentsFilter') }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="col-lg-2 mb-2 mb-lg-0">
                                        <select class="form-control" id="sort-comments" name="sort_comments">
                                            <option value="desc" @if(!request('sort_comments') || (request('sort_comments') == 'desc')) selected @endif>
                                                {{ __('custom.fromNewest') }}
                                            </option>
                                            <option value="asc" @if((request('sort_comments') == 'asc')) selected @endif>
                                                {{ __('custom.fromOldest') }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="col-lg-2 mb-lg-0 mb-2">
                                        <button type="submit" class="btn btn-primary form-control">
                                            {{ __('custom.filter') }}
                                            <svg class="bi bi-filter" width="1.5em" height="1.5em" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M7.5 13a.5.5 0 01.5-.5h4a.5.5 0 010 1H8a.5.5 0 01-.5-.5zm-2-3a.5.5 0 01.5-.5h8a.5.5 0 010 1H6a.5.5 0 01-.5-.5zm-2-3a.5.5 0 01.5-.5h12a.5.5 0 010 1H4a.5.5 0 01-.5-.5z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                        @if(!empty(request('filter_comments_query')) ||
                                           !empty(request('filter_comments')) ||
                                           !empty(request('sort_comments')))
                                            <div style="text-align: center;">
                                                <button class="btn btn-link" id="undo-filter-comments">
                                                    {{ __('custom.resetFilters') }}
                                                </button>
                                            </div>
                                        @endif

                                    </div>
                                </div>
                            </form>

                            <hr/>

                            @if(count($comments))
                                <table class="table table-striped table-bordered comments-table">
                                    <tbody>
                                    @foreach ($comments as $_i => $_comment)

                                        <tr id="comment{{$_comment->id}}">
                                            <td>
                                                @if(!$_comment->hasDocumentation())
                                                <p class="mb-0 ml-2 float-right">
                                                    <a title="{{ __('custom.sendReminder') }}" data-id="{{ $_comment->id }}" class="send-notification btn btn-primary btn-xs">
                                                        <svg class="bi bi-bell-fill" width="1em" height="1em" viewBox="0 0 20 20" fill="white" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M10 18a2 2 0 002-2H8a2 2 0 002 2zm.995-14.901a1 1 0 10-1.99 0A5.002 5.002 0 005 8c0 1.098-.5 6-2 7h14c-1.5-1-2-5.902-2-7 0-2.42-1.72-4.44-4.005-4.901z"></path>
                                                        </svg>
                                                    </a>
                                                </p>
                                                <p class="mb-0 ml-2 float-right">
                                                    <a  title="{{ __('custom.createDocumentation') }}" data-id="{{ $_comment->id }}" class="create-documentation btn btn-primary btn-xs">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="white" class="bi bi-folder-fill" viewBox="0 0 16 16">
                                                            <path d="M9.828 3h3.982a2 2 0 0 1 1.992 2.181l-.637 7A2 2 0 0 1 13.174 14H2.825a2 2 0 0 1-1.991-1.819l-.637-7a1.99 1.99 0 0 1 .342-1.31L.5 3a2 2 0 0 1 2-2h3.672a2 2 0 0 1 1.414.586l.828.828A2 2 0 0 0 9.828 3zm-8.322.12C1.72 3.042 1.95 3 2.19 3h5.396l-.707-.707A1 1 0 0 0 6.172 2H2.5a1 1 0 0 0-1 .981l.006.139z"/>
                                                        </svg>
                                                    </a>
                                                </p>
                                                <p class="mb-0 ml-3 float-right">
                                                    <a  title="{{ __('custom.createTaskFromComment') }}" data-id="{{ $_comment->id }}" class="create-task btn btn-primary btn-xs">
                                                        <svg class="bi bi-alert-octagon-fill" width="1em" height="1em" viewBox="0 0 20 20" fill="white" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M13.107 2a.5.5 0 01.353.146l4.394 4.394a.5.5 0 01.146.353v6.214a.5.5 0 01-.146.353l-4.394 4.394a.5.5 0 01-.353.146H6.893a.5.5 0 01-.353-.146L2.146 13.46A.5.5 0 012 13.107V6.893a.5.5 0 01.146-.353L6.54 2.146A.5.5 0 016.893 2h6.214zM9.002 13a1 1 0 112 0 1 1 0 01-2 0zM10 6a.905.905 0 00-.9.995l.35 3.507a.553.553 0 001.1 0l.35-3.507A.905.905 0 0010 6z" clip-rule="evenodd"></path>
                                                        </svg>
                                                    </a>
                                                </p>
                                                @endif

                                                <span class="font-weight-bold">
                                                    <img class="user-avatar" src="{{ Avatar::create($_comment->user->name)->toBase64() }}" />
                                                    <a class="comment-link" href="{{ route('comments.show', $_comment) }}">
                                                        {{ $_comment->user->name }} @ {{ $_comment->updated_at }}
                                                    </a>
                                                </span>
                                                <hr/>

                                                @if($_comment->referencedComment)

                                                    <div class="bordered-div mb-4" style="background: @if($viewData['themeName'] == 'default-dark') #444444 @elseif($_i%2 == 0) #ffffff @else #f7f7f7 @endif">
                                                        <img class="user-avatar" src="{{ Avatar::create($_comment->referencedComment->user->name)->toBase64() }}" />
                                                        <a class="comment-link" href="{{ route('comments.show', $_comment->referencedComment) }}">
                                                            <span class="font-weight-bold">
                                                                {{ $_comment->referencedComment->user->name }} @ {{ $_comment->referencedComment->updated_at }}
                                                            </span>
                                                        </a>

                                                        <hr/>

                                                        @if(!empty(strip_tags($_comment->referencedComment->comment, '<img>')))
                                                            <div class="referenced-comment">
                                                                <div class="comment-content collapsed">
                                                                    {!! $_comment->referencedComment->comment !!}
                                                                    @if($_comment->referencedComment->hasDocumentation())
                                                                        <div>
                                                                            <ul>
                                                                                <li>
                                                                                    <label class="font-weight-bold">
                                                                                        {{ __('custom.documentation') }}:
                                                                                        <a target="_blank" href="{{ route('documentation.show', [$_comment->referencedComment->documentation->id]) }}">
                                                                                            {{ $_comment->referencedComment->documentation->title }}
                                                                                        </a>
                                                                                    </label>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    @endif

                                                                    @if(count($_comment->referencedComment->uploads))
                                                                        <ul>
                                                                            @foreach($_comment->referencedComment->uploads as $_upload)
                                                                                <li>
                                                                                    <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                                                        {{ $_upload->name }}
                                                                                    </a>
                                                                                </li>
                                                                            @endforeach
                                                                        </ul>
                                                                    @endif
                                                                </div>
                                                                <a class="expand-comment d-block pt-3" style="display:none!important;">Prikaži komentar</a>
                                                            </div>

                                                        @endif
                                                    </div>
                                                @endif

                                                @if(!empty(strip_tags($_comment->comment, '<img>')))
                                                    <div class="comment-content mb-4">
                                                        {!! $_comment->comment !!}
                                                    </div>
                                                @endif
                                                @if($_comment->hasDocumentation())
                                                    <div>
                                                        <ul>
                                                            <li>
                                                                <label class="font-weight-bold">
                                                                    {{ __('custom.documentation') }}:
                                                                    <a target="_blank" href="{{ route('documentation.show', [$_comment->documentation->id]) }}">
                                                                        {{ $_comment->documentation->title }}
                                                                    </a>
                                                                </label>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                @endif
                                                @if(count($_comment->uploads))
                                                    <ul>
                                                        @foreach($_comment->uploads as $_upload)
                                                            <li>
                                                                <a target="_blank" href="{{ route('resource.download', $_upload->id) }}">
                                                                    {{ $_upload->name }}
                                                                </a>
                                                            </li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                                    <hr/>
                                                @if($_comment->belongsToUser(Auth::user()))
                                                    <a href="{{ route('comments.edit', [$_comment->id]) }}" class="btn btn-link">{{ __('custom.edit') }}</a>
                                                    <a data-confirmation="{{ __('custom.pleaseConfirm') }}" href="{{ route('comments.destroy', [$_comment->id]) }}" class="pleaseConfirm btn btn-link">{{ __('custom.delete') }}</a>
                                                @endif

                                                <button data-header="{{ __('custom.replyReference') }} {{ $_comment->user->name }} @ {{ $_comment->updated_at }}" data-id="{{ $_comment->id }}" class="btn btn-default btn-reply">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-reply-fill" viewBox="0 0 16 16" style="margin-bottom: 3px;">
                                                        <path d="M5.921 11.9 1.353 8.62a.719.719 0 0 1 0-1.238L5.921 4.1A.716.716 0 0 1 7 4.719V6c1.5 0 6 0 7 8-2.5-4.5-7-4-7-4v1.281c0 .56-.606.898-1.079.62z"/>
                                                    </svg>
                                                    @if(!$agent->isMobile() || !$_comment->belongsToUser(Auth::user()))
                                                        {{ __('custom.reply') }}
                                                    @endif
                                                </button>

                                                <button data-toggle="tooltip" data-placement="right" title="{{ $_comment->getUserLikesNames() }}" type="button" class="btn {{ in_array(Auth::id(), $_comment->getUserLikesIds()) ? 'btn-primary like-comment' : 'btn-default like-comment' }}" data-id="{{ $_comment->id }}">
                                                    <span class="like-count">{{ count($_comment->getUserLikesIds()) }}</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-hand-thumbs-up-fill" viewBox="0 0 16 16">
                                                        <path d="M6.956 1.745C7.021.81 7.908.087 8.864.325l.261.066c.463.116.874.456 1.012.965.22.816.533 2.511.062 4.51a9.84 9.84 0 0 1 .443-.051c.713-.065 1.669-.072 2.516.21.518.173.994.681 1.2 1.273.184.532.16 1.162-.234 1.733.***************.138.363.077.27.113.567.113.856 0 .289-.036.586-.113.856-.039.135-.09.273-.16.404.169.387.107.819-.003 1.148a3.163 3.163 0 0 1-.488.901c.054.152.076.312.076.465 0 .305-.089.625-.253.912C13.1 15.522 12.437 16 11.5 16H8c-.605 0-1.07-.081-1.466-.218a4.82 4.82 0 0 1-.97-.484l-.048-.03c-.504-.307-.999-.609-2.068-.722C2.682 14.464 2 13.846 2 13V9c0-.85.685-1.432 1.357-1.615.849-.232 1.574-.787 2.132-1.41.56-.627.914-1.28 1.039-1.639.199-.575.356-1.539.428-2.59z"></path>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>

                                <div class="row">
                                    <div class="col-lg-12">
                                        {{ $comments->appends($_GET)->links() }}
                                    </div>
                                </div>

                            @else
                                {{ __('custom.noComments') }}
                            @endif
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <div id="categories-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.selectCategories') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <input class="form-control" id="category-search" type="text" placeholder="Pretraži...">
                            <div class="mt-3" id="category-tree"></div>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button id="edit-categories-submit" type="button" class="btn btn-primary">{{ __('custom.submit') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div id="documentation-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="create-documentation-form">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('custom.createDocumentationFromComment') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label for="title">{{ __('custom.title') }}</label>
                                    <input autocomplete="off" name="title" required="required" id="documentation-from-comment-title" type="text" class="form-control" value="">
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">{{ __('custom.createDocumentation') }}</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div id="create-documentation-success" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.documentationCreated') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-success">
                                {{  substr(__('custom.documentationCreated'), 0, strrpos(__('custom.documentationCreated'), ' ')) }}
                                <a href="" id="created-documentation-url">{{ substr(__('custom.documentationCreated'), strrpos(__('custom.documentationCreated'), ' ') + 1) }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                </div>
            </div>
        </div>
    </div>


    <div id="task-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="create-task-form">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('custom.createTaskFromComment') }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="form-group">
                                    <label for="title">{{ __('custom.title') }}</label>
                                    <span class="d-none" id="new-task-default-title">{{ __('custom.newTask') . " - " . $topic->title }}</span>
                                    <input autocomplete="off" name="task_title" required="required" id="task-title" type="text" class="form-control" value="">
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">{{ __('custom.createTask') }}</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div id="create-task-success" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.taskCreated') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-success">
                                {{  substr(__('custom.taskCreated'), 0, strrpos(__('custom.taskCreated'), ' ')) }}
                                <a href="" id="created-task-url">{{ substr(__('custom.taskCreated'), strrpos(__('custom.taskCreated'), ' ') + 1) }}</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div id="reminder-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.sendReminder') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <select class="form-control" style="width: 100%;" id="reminder-user-picker" multiple >
                                <option></option>
                                @foreach($topic->users as $_user)
                                    <option value="{{ $_user->id }}">{{ $_user->name }}</option>
                                @endforeach
                            </select>
                            <span id="reminder-user-picker-error" class="error d-none">
                                <small>
                                    {{ __('custom.requiredField') }}
                                </small>
                            </span>
                            <textarea rows="3" class="form-control mt-2" placeholder="{{ __('custom.addMessage') }}..." id="reminderNote"></textarea>
                        </div>
                    </div>

                </div>
                <div class="modal-footer">
                    <button id="send-reminder" type="button" class="btn btn-primary">{{ __('custom.send') }}</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.cancel') }}</button>
                </div>
            </div>
        </div>
    </div>
    <div id="send-reminder-success" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.reminderSent') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-success">
                                {{ __('custom.reminderSent') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                </div>
            </div>
        </div>
    </div>
    <div id="categories-edited-success" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('custom.success') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-success">
                                {{ __('custom.categoriesEdited') }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                </div>
            </div>
        </div>
    </div>
    <div id="category-topics-modal" class="modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="category-topics-header"></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div id="category-topics-table-div" style="padding-top:1px;">
                                <table class="table table-borderless table-striped" id="category-topics-table">
                                    <thead>
                                    <tr>
                                        <th>id</th>
                                        <th>{{ __( 'custom.topic') }}</th>
                                        <th>{{ __( 'custom.type') }}</th>
                                        <th>{{ __( 'custom.priority') }}</th>
                                        <th>{{ __( 'custom.status') }}</th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('custom.close') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function(){

            $('.referenced-comment').each(function() {
                let commentContent = $(this).find('.comment-content');
                let expandButton = $(this).find('.expand-comment');

                if (commentContent.outerHeight() >= 150) {
                    expandButton.show();
                }
            });

            $('.expand-comment').click(function(event) {
                event.preventDefault();
                let content = $(this).parents('.referenced-comment').find('.comment-content');

                if (content.hasClass('collapsed')) {
                    content.removeClass('collapsed');
                    $(this).remove();
                } else {
                    content.addClass('collapsed');
                    $(this).text('Prikaži komentar');
                }
            });

            $('[data-toggle="tooltip"]').tooltip();

            // on category assign, refresh page
            $('#categories-edited-success').on('hidden.bs.modal', function () {
                location.reload();
            });

            // category badge popup
            $('.category-badge').on('click', function(e) {

                let category_id = $(this).data('id');
                let category_name = $(this).html();

                $('#category-topics-header').html(
                    '<span class="badge badge-primary category-badge">'
                    +category_name+
                    '</span>');

                initDataTable(category_id);

                $('#category-topics-modal').modal()
            })

            $('#category-topics-modal').on('hidden.bs.modal', function () {
                // destroy datatable
                $('#category-topics-table').DataTable().destroy();
            });

            function initDataTable(category_id)
            {
                let route = '{!! route('topics.list', ['status' => 'active']) !!}';

                let ajax_route = route+"&categories="+category_id;

                $('#category-topics-table-div').hide();

                return $('#category-topics-table').DataTable({
                    language: { url: '{!! route('dataTables.languageFile') !!}' },
                    autoWidth: false,
                    responsive: {
                        details: false

                    },
                    pageLength: 10,
                    order: [],
                    processing: false,
                    serverSide: true,
                    ajax: {
                        url: ajax_route,
                        cache: true
                    },
                    initComplete: function() {
                        $('#category-topics-table-div').fadeIn(100);
                    },
                    columns: [
                        { data: 'id' },
                        {
                            data: 'title',
                            name: 'title',
                            type: 'html',
                            render: function(data, type, row) {
                                return '<a class="dt-link" href="'+ row['show_link'] +'">' + data + '</a>';
                            }
                        },
                        {
                            data: 'type_id', name: 'type_id',
                        },
                        {
                            data: 'priority',
                            type: 'html',
                            name: 'priority_id',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['priority']+'</span>';
                            }
                        },
                        {
                            data: 'status_id',
                            sortable: false,
                            type: 'html',
                            render: function(data, type, row) {
                                return '<span class="'+data['class']+'">'+data['status']+'</span>';
                            }
                        },
                    ],
                    columnDefs: [
                        {
                            targets: [ 0],
                            visible: false,
                        },
                        {
                            targets: [2,3,4],
                            visible: !isMobile()
                        },
                        {
                            targets: [ 0, 2, 3, 4],
                            searchable: false
                        },
                    ],
                });
            }

            initializeTextEditor(
                '#comment',
                '{{ route('image.upload') }}',
                '{{ csrf_token() }}',
                {{ $viewData['themeName'] == 'default-dark' }}
            );

            initializeTextEditor(
                '#content',
                '{{ route('image.upload') }}',
                '{{ csrf_token() }}',
                {{ $viewData['themeName'] == 'default-dark' }}
            );

            initializeTextEditor(
                '#documentation-comment',
                '{{ route('image.upload') }}',
                '{{ csrf_token() }}',
                {{ $viewData['themeName'] == 'default-dark' }}
            );


            $('#comment-filter').select2({minimumResultsForSearch: -1});
            $('#sort-comments').select2({minimumResultsForSearch: -1});

            let reminder_user_picker = $('#reminder-user-picker').select2({
                placeholder: "{{ __('custom.selectUsers') }}...",
                minimumResultsForSearch: -1,
                closeOnSelect: false,
                dropdownParent: $('#reminder-modal')
            });

            let comment_id = null;

            $('.create-task').on('click', function(e){
                comment_id = $(this).data('id');

                // populate default task name
                $('#task-title').val($('#new-task-default-title').html())

                $('#task-modal').modal()
                $('#task-title').focus();
            });

            $('#create-task-form').on('submit', function(e){
                e.preventDefault();
                let title = $('#task-title').val();

                $.post( "{{ route('topics.createTaskFromComment') }}",
                    {
                        'comment_id': comment_id,
                        'title': title,
                        '_token': $('meta[name="csrf-token"]').attr('content')
                    },
                    function(created_task_url) {
                        $('#task-modal').modal('hide');
                        $('#created-task-url').attr('href', created_task_url);
                        $('#create-task-success').modal();
                    }
                );
            });

            $('.create-documentation').on('click', function(e){
                comment_id = $(this).data('id');

                $('#documentation-modal').modal()
                $('#documentation-from-comment-title').focus();
            });

            $('#create-documentation-form').on('submit', function(e){
                e.preventDefault();
                let title = $('#documentation-from-comment-title').val();

                $.post( "{{ route('documentation.createDocumentationFromComment') }}",
                    {
                        'comment_id': comment_id,
                        'title': title,
                        '_token': $('meta[name="csrf-token"]').attr('content')
                    },
                    function(created_documentation_url) {
                        $('#documentation-modal').modal('hide');
                        $('#created-documentation-url').attr('href', created_documentation_url);
                        $('#create-documentation-success').modal();
                    }
                );
            });

            $('.send-notification').on('click', function(e){
                comment_id = $(this).data('id');

                $('#reminder-user-picker-error').addClass('d-none');
                $('#reminder-modal').modal()
            });

            $('#send-reminder').on('click', function(e){

                let user_ids = reminder_user_picker.val();
                let message = $('#reminderNote').val();

                if(user_ids.length) {
                    $('#reminder-user-picker-error').addClass('d-none');

                    $.post( "{{ route('comments.reminder') }}",
                        {
                            'id': comment_id,
                            'users': user_ids,
                            'message': message,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function(data) {
                            $('#reminder-modal').modal('hide');
                            $('#send-reminder-success').modal();
                        }
                    );
                }
                else{
                    $('#reminder-user-picker-error').removeClass('d-none');
                }
            });

            $('#undo-filter-comments').on('click', function(e){
                e.preventDefault();
                // reload page without filters
                window.location.href = "{{ route('topics.show', $topic) }}";
            });

            $('#documentation-new-picker').select2({minimumResultsForSearch: -1}).on('change', function(e){

                if($(this).val() == "new"){
                    $('#newDocumentationFields').removeClass('d-none');
                    $('#existingDocumentationFields').addClass('d-none');
                }
                else{
                    $('#newDocumentationFields').addClass('d-none');
                    $('#existingDocumentationFields').removeClass('d-none');
                }
            });

            $('#documentation-picker').select2({
                placeholder: '{{ __('custom.search') }}...',
                minimumInputLength: 3,
                ajax: {
                    url: '{{ route('documentation.search') }}',
                    dataType: 'json',
                    delay: 200,
                }
            });

            $('#responsible').select2({closeOnSelect: false});

            $('#deadline').datepicker({
                minDate: 0,
                dateFormat: 'dd.mm.yy.'
            });

            $('.submit-documentation').on('click', function(e){
               let name = $(e.target).attr('name');
               $('#submit-documentation').attr('name', name);
                $('#documentation-comment-modal').modal();
            });

            $('#edit-categories').on('click', function(e){
               $('#categories-modal').modal();
            });

            $('#category-tree')
                .jstree({
                    core : {
                        data : @json($topic->getCategoryTree()),
                        themes: {
                            name: "{{ $viewData['themeName'] }}",
                            dots: true,
                            icons: true
                        },
                    },
                    checkbox : {
                        three_state : false
                    },
                    plugins : ['checkbox', 'search']
            })
            .on('changed.jstree', function (e, data) {
                if(data.action === 'select_node'){
                    // select parent and the event will propagate further
                    $(this).jstree(true).select_node(data.node.parent);
                }
            });

            let to = false;
            $('#category-search').keyup(function () {
                if(to) { clearTimeout(to); }
                to = setTimeout(function () {
                    var v = $('#category-search').val();
                    $('#category-tree').jstree('search', v);
                }, 100);
            });

            $('#edit-categories-submit').on('click', function(e){
                let categories = $("#category-tree").jstree("get_checked",null,true);

                $.post( "{{ route('topics.editCategories') }}",
                    {
                        'id': '{{ $topic->id }}',
                        'categories': categories ,
                        '_token': $('meta[name="csrf-token"]').attr('content')
                    },
                    function(data) {
                        $('#categories-modal').modal('hide');
                        $('#categories-edited-success').modal();
                    }
                );
            });

            $('.like-comment').on('click', function(e){

                $(this).tooltip('hide');

                let element = $(this);
                let commentId = $(this).data('id');

                // like
                if($(this).hasClass('btn-default')) {

                    $.post( "{{ route('comments.like') }}",
                        {
                            'id': commentId,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function(data) {
                            // update counter, like names
                            element.find('.like-count').html(data['like_count']);
                            element.attr('data-original-title', data['like_names']);

                            // toggle color
                            element.removeClass('btn-default').addClass('btn-primary');
                        }
                    );

                } else { // unlike

                    $.post( "{{ route('comments.unlike') }}",
                        {
                            'id': commentId,
                            '_token': $('meta[name="csrf-token"]').attr('content')
                        },
                        function(data) {
                            // update counter, like names
                            element.find('.like-count').html(data['like_count']);
                            element.attr('data-original-title', data['like_names']);

                            // toggle color
                            element.removeClass('btn-primary').addClass('btn-default');
                        }
                    );
                }

            });

            $('.btn-reply').on('click', function(e){

                $('#ref-div').html('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-reply-fill" style="margin-bottom: 3px;" viewBox="0 0 16 16"> ' +
                    '<path d="M5.921 11.9 1.353 8.62a.719.719 0 0 1 0-1.238L5.921 4.1A.716.716 0 0 1 7 4.719V6c1.5 0 6 0 7 8-2.5-4.5-7-4-7-4v1.281c0 .56-.606.898-1.079.62z"/> </svg> ' +
                    '<a href="" data-id="'+$(this).data('id')+'" class="comment-reference-link">'+$(this).data('header')+'</a>  <a class="btn btn-xs btn-primary remove-comment-reference" href="#">x</a>').removeClass('d-none');

                $('.ref-id').val($(this).data('id'));
                $('.ref-header').val($(this).data('header'));

                bindCommentReferenceLink();
                bindRemoveCommentReferenceLink();

                $('#commentBox').addClass('show');

                $([document.documentElement, document.body]).animate({
                    scrollTop: $("#commentBox").offset().top-130
                }, 200);
            });

            function bindCommentReferenceLink()
            {
                $('.comment-reference-link').unbind('click').on('click', function(e){
                    e.preventDefault();

                    let comment_id = $(this).data('id');

                    $([document.documentElement, document.body]).animate({
                        scrollTop: $("#comment"+comment_id).offset().top-70
                    }, 200);
                });
            }

            bindCommentReferenceLink();


            function bindRemoveCommentReferenceLink()
            {
                $('.remove-comment-reference').unbind('click').on('click', function(e){
                    e.preventDefault();

                    $('#ref-div').html('');
                    $('#ref-div').addClass('d-none');

                    $('.ref-id').val(null);
                    $('.ref-header').val(null);

                });
            }

            bindRemoveCommentReferenceLink();

            function bindImagesToContainers() {
                $(".comment-content table").wrap("<div class='table-responsive'></div>");
                $("img, video").addClass("img-responsive").css({
                    "max-width": "100%",
                    "height": "auto"
                });
            }

            bindImagesToContainers();

        });
    </script>
@endpush

