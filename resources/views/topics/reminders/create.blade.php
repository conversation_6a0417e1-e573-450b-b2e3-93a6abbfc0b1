@extends('layouts.app')
@section('title') {{ __('custom.addReminder') }} - @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <span>{{ __('custom.addReminder') }}</span>
                    </div>

                    <div class="card-body">

                        <form autocomplete="off" method="POST" action="{{ route('topics.reminders.store', [$topic->id]) }}" enctype="multipart/form-data">
                            @csrf
                            <div class="form-group">
                                <span class="font-weight-bold">{{ __('custom.topic') }}: </span>
                                <a href="{{ route('topics.show', [$topic->id]) }}">{{ $topic->title }}</a>
                            </div>

                            <hr/>

                            <div class="form-group">
                                <label for="message">{{ __('custom.message') }}</label>
                                <input required class="form-control" type="text" name="message" value="{{old('message')}}">
                                @if($errors->has('message'))
                                    <div class="error">{{ $errors->first('message') }}</div>
                                @endif
                            </div>

                            <div class="form-group">
                                <label for="frequency_id">{{ __('custom.frequency') }}</label>
                                <select name="frequency_id" id="frequency" class="form-control">
                                    @foreach(\App\TopicReminder::$frequencies as $_name => $_id)
                                        <option
                                            value="{{ $_id }}"
                                            @if($_id == old('frequency_id', \App\TopicReminder::$frequencies['once'])) selected @endif
                                        >
                                            {{ __('custom.'.$_name) }}
                                        </option>
                                    @endforeach
                                </select>
                                @if($errors->has('frequency_id'))
                                    <div class="error">{{ $errors->first('frequency_id') }}</div>
                                @endif
                            </div>

                            <div id="frequency_once_container" style="{{ !empty(old('frequency_id')) && old('frequency_id') != '1' ? 'display:none;' : ''  }}">
                                <div class="form-group">
                                    <label for="date">{{ __('custom.date') }}</label>
                                    <input value="{{old('date')}}" class="form-control" name="date" type="text" id="date-picker">
                                    @if($errors->has('date'))
                                        <div class="error">{{ $errors->first('date') }}</div>
                                    @endif
                                </div>

                            </div>

                            <div id="frequency_days_container" style="{{ old('frequency_id') != 3 ? 'display:none;' : ''  }}">
                                <div class="form-group">
                                    <label for="day_of_the_week">{{ __('custom.dayOfTheWeek') }}</label>
                                    <select style="width: 100%" name="day_of_the_week" class="s2 form-control">
                                        <option value="1">{{__('custom.monday')}}</option>
                                        <option value="2">{{__('custom.tuesday')}}</option>
                                        <option value="3">{{__('custom.wednesday')}}</option>
                                        <option value="4">{{__('custom.thursday')}}</option>
                                        <option value="5">{{__('custom.friday')}}</option>
                                        <option value="6">{{__('custom.saturday')}}</option>
                                        <option value="7">{{__('custom.sunday')}}</option>
                                    </select>
                                </div>
                            </div>

                            <div id="frequency_months_container" style="{{ old('frequency_id') != 4 ? 'display:none;' : ''  }}">
                                <div class="form-group">
                                    <label for="day_of_the_month">{{ __('custom.dayOfTheMonth') }}</label>
                                    <select style="width: 100%" name="day_of_the_month" class="s2 form-control">
                                        @for($i=1; $i < 32; $i++)
                                            <option value="{{ $i }}">{{$i}}</option>
                                        @endfor
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">

                                <label for="recipients">{{ __('custom.recipients') }}</label>

                                <select multiple="multiple" class="form-control" id="recipients" name="recipients[]">
                                    @foreach($topic->users as $_user)
                                        <option @if(!empty(old('recipients')) && in_array($_user->id, old('recipients'))) selected @endif value="{{ $_user->id }}">{{ $_user->name }}</option>
                                    @endforeach
                                </select>
                                @if($errors->has('recipients'))
                                    <div class="error">{{ $errors->first('recipients') }}</div>
                                @endif
                            </div>

                            <div class="form-group">
                                <label for="time_hours">{{ __('custom.time') }}</label>
                                <div class="row">
                                    <div class="col-lg-6 col-sm-6 mb-2">
                                        <div class="input-group">
                                            <input required value="{{ old('time_hours', '08') }}" name="time_hours" type="number" class="form-control" id="time_hours" min="0" max="24">
                                            <div class="input-group-append">
                                                <span class="input-group-text">{{__('custom.hours')}}</span>
                                            </div>
                                        </div>
                                        @if($errors->has('time_hours'))
                                            <div class="error">{{ $errors->first('time_hours') }}</div>
                                        @endif

                                    </div>
                                    <div class="col-lg-6 col-sm-6">
                                        <div class="input-group">
                                            <input required value="{{ old('time_minutes', '00') }}" name="time_minutes" type="number" class="form-control" id="time_minutes" min="0" max="60">
                                            <div class="input-group-append">
                                                <span class="input-group-text">{{__('custom.minutes')}}</span>
                                            </div>
                                        </div>
                                        @if($errors->has('time_minutes'))
                                            <div class="error">{{ $errors->first('time_minutes') }}</div>
                                        @endif

                                    </div>
                                </div>


                            </div>

                            <div class="form-group">
                                <input value="{{ __('custom.submit') }}" type="submit" class="btn btn-primary btn-block">
                            </div>

                        </form>


                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function() {

            $('.s2').select2({minimumResultsForSearch: -1});
            $('#recipients').select2({closeOnSelect: false});

            let frequency_picker = $('#frequency').select2({minimumResultsForSearch: -1});

            frequency_picker.on('change', function(e){

                $('#frequency_once_container').hide();
                $('#frequency_days_container').hide();
                $('#frequency_months_container').hide();

                // once
                if(this.value == 1) {
                    $('#frequency_once_container').show();
                }
                // weekly
                else if(this.value == 3) {
                    $('#frequency_days_container').show();
                } // monthly
                else if(this.value == 4) {
                    $('#frequency_months_container').show();
                }
            });

            $('#date-picker').datepicker({
                minDate: 0,
                dateFormat: 'dd.mm.yy.'
            });

        });
    </script>
@endpush
