<?php

return [
    'askPlaceholder' => 'Ask something...',
    'save' => 'Save',
    'notificationsHeader' => 'Notifications',
    'access' => 'Access',
    'editing' => 'Editing',
    'noCategoriesExist' => 'No categories exist.',
    'categoryMoveSuccess' => 'Category moved successfully.',
    'reminder' => 'Reminder',
    'recipients' => 'Recipients',
    'activate' => 'Activate',
    'pause' => 'Pause',
    'topicReminderSet' => 'Reminder has been added successfully.',
    'topicReminderUpdated' => 'Reminder has been updated successfully.',
    'topicReminderPaused' => 'Reminder has been paused successfully..',
    'topicReminderDeleted' => 'Reminder has been deleted succesfully.',
    'topicReminderActivated' => 'Reminder has been activated successfully.',
    'dayOfTheWeek' => 'Day of the week',
    'dayOfTheMonth' => 'Day of the month',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    'sunday' => 'Sunday',
    'date' => 'Date',
    'hours' => 'Hours',
    'minutes' => 'Minutes',
    'time' => 'Time',
    'frequency' => 'Frequency',
    'once' => 'Once',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly',
    'editReminder' => 'Edit reminder',
    'addReminder' => 'Add reminder',
    'assign' => 'Assign',
    'assignCategories' => 'Assign categories',
    'deletingCategoryWillDeleteSubcategories' => 'Deleting a category will delete its subcategories.',
    'parent' => 'Parent',
    'iUnderstand' => 'I understand',
    'youHaveToSelectACategory' => 'You have to select a category',
    'youHaveToSelectAnItem' => 'You have to select an item',
    'myProfile' => 'My profile',
    'message' => 'Message',
    'addMessage' => 'Add message',
    'unCategorized' => 'Uncategorized',
    'searchTopics' => 'Search topics',
    'filterByTopics' => 'Filter by topics',
    'categories' => 'Categories',
    'allCategoriesNav' => 'All Categories',
    'newCategoryNav' => 'New category',
    'content' => 'Content',
    'addNewDocumentation' => 'Add new documentation',
    'editDocumentation' => 'Edit documentation',
    'deleteDocumentation' => 'Delete documentation',
    'documentationEdited' => 'Documentation was edited successfully.',
    'documentationDeleted' => 'Documentation was deleted successfully.',
    'newDocumentation' => 'New documentation',
    'existingDocumentation' => 'Existing documentation',
    'newDocumentationNav' => 'Add new',
    'allDocumentationNav' => 'Documentation',
    'requiredField' => 'This field is required.',
    'selectUsers' => 'Select users',
    'selectCategory' => 'Select category',
    'selectCategories' => 'Select categories',
    'categoriesEdited' => 'Categories edited successfully.',
    'success' => 'Success',
    'categoriesSelectedCount' => 'Selected',
    'sendReminder' => 'Send reminder',
    'reminderSent' => 'Reminder sent',
    'send' => 'Send',
    'cancel' => 'Cancel',
    'undo' => 'Undo',
    'search' => 'Search',
    'searchPage' => 'Search',
    'searchPlaceholder' => 'Type search query...',
    'filter' => 'Filter',
    'select...' => 'Select...',
    'filterAll' => 'All',
    'myCommentsFilter' => 'My comments',
    'noData' => 'No data',
    'noComments' => 'No comments published yet.',
    'comments' => 'Comments',
    'documentation' => 'Documentation',
    'addDocumentation' => 'Add documentation',
    'addTask' => 'Add task',
    'createTask' => 'Create task',
    'createTaskFromComment' => 'Create task from comment',
    'editTask' => 'Edit task',
    'closeTask' => 'Close task',
    'reopenTask' => 'Reopen task',
    'destroyTask' => 'Destroy task',
    'documentationCreated' => 'Documentation was created successfully.',
    'topics' => 'Topics',
    'newTopic' => 'New topic',
    'newTask' => 'New task',
    'menu' => 'Menu',
    'user' => 'User',
    'status' => 'Status',
    'deadline' => 'Deadline',
    'title' => 'Title',
    'description' => 'Description',
    'submit' => 'Submit',
    'users' => 'Users',
    'task' => 'Task',
    'taskCreated' => 'Task was created successfully.',
    'taskEdited' => 'Task was edited successfully.',
    'taskDeleted' => 'Task was deleted successfully.',
    'project' => 'Project',
    'projects' => 'Projects',
    'creator' => 'Creator',
    'participants' => 'Participants',
    'files' => 'Files',
    'topic' => 'Topic',
    'allTopics' => 'All',
    'allTopicsNav' => 'All topics',
    'allTasksNav' => 'All tasks',
    'activeTopics' => 'Active',
    'completedTopics' => 'Completed',
    'priority' => 'Priority',
    'type' => 'Type',
    'topicCreated' => 'Topic was created successfully.',
    'topicEdited' => 'Topic was edited successfully.',
    'topicDeleted' => 'Topic was deleted successfully.',
    'editTopic' => 'Edit topic',
    'destroyTopic' => 'Delete topic',
    'pleaseConfirm' => 'Are you sure?',
    'confirm' => 'Confirm',
    'active' => 'Active',
    'paused' => 'Paused',
    'completed' => 'Completed',
    'low' => 'Low',
    'medium' => 'Medium',
    'high' => 'High',
    'urgent' => 'Urgent',
    'bug' => 'Bug',
    'feature' => 'Feature',
    'improvement' => 'Improvement',
    'createdAt' => 'Created at',
    'updatedAt' => 'Updated at',
    'responsible' => 'Responsible',
    'myTopics' => 'My topics',
    'chooseAction' => 'Choose action',
    'choose' => 'Choose',
    'seeAll' => '... see all',
    'uncheckAll' => 'Uncheck all',
    'closeTopic' => 'Close topic',
    'reopenTopic' => 'Reopen topic',
    'leaveTopic' => 'Leave topic',
    'leftTopic' => 'You have left the topic.',
    'seeTopics' => 'See topics',
    'showMyTopics' => 'Show my topics',
    'showTasks' => 'Show tasks',
    'showOnlyMyTasks' => 'Show only my tasks',
    'ownTopics' => 'Own',
    'discussion' => 'Discussion',
    'addComment' => 'Add comment',
    'addNewCategory' => 'Add new category',
    'editCategory' => 'Edit category',
    'editCategories' => 'Edit categories',
    'topicCommentCreated' => 'Comment published successfully.',
    'topicCommentEdited' => 'Comment edited successfully.',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'remove' => 'Remove',
    'topicCommentDeleted' => 'Comment deleted successfully.',
    'editTopicComment' => 'Edit topic comment',
    'comment' => 'Comment',
    'selectAllRows' => 'Select all rows',
    'selectAction' => 'Quick actions...',
    'close' => 'Close',
    'reopen' => 'Reopen',
    'notifications' => [
        'greeting' => 'Notification',
        'topicCommentReminder' => [
            'subject' => 'You have a reminder from user',
            'intro' => 'has sent you reminder for a comment on a topic you participate on',
            'actionText' => 'See comment',
        ],
        'topicCommentCreated' => [
            'subject' => 'New comment on topic',
            'intro' => 'New comment has been posted on a topic you participate on',
            'actionText' => 'Open topic',
        ],
        'topicCommentUpdated' => [
            'subject' => 'Comment updated on topic',
            'intro' => 'Comment has been updated on a topic you participate on',
            'actionText' => 'Open topic',
        ],
        'topicCommentDeleted' => [
            'subject' => 'Comment deleted',
            'intro' => 'Comment has been deleted on a topic you participate on',
            'actionText' => 'Open topic',
            'deletedComment' => 'Deleted comment',
        ],
        'topicCreated' => [
            'subject' => 'New topic',
            'intro' => 'You have been assigned to a new topic',
            'actionText' => 'Open topic',
        ],
        'topicDeleted' => [
            'subject' => 'Topic deleted',
            'intro' => 'Topic you participated on was deleted'
        ],
        'topicStatusChanged' => [
            'subject' => 'Topic status was updated',
            'intro' => 'Status of a topic you participate on has been updated',
            'newStatus' => 'New status',
            'actionText' => 'Open topic',
        ],
        'topicUpdated' => [
            'subject' => 'Topic updated',
            'intro' => 'Topic you participate on has been updated',
            'actionText' => 'Open topic',
        ],
        'deadlineExpirationWarning' => [
            'subject' => 'Deadline for task is approaching',
            'intro' => 'Deadline for a task you participate on is approaching quickly!',
            'actionText' => 'Open task'
        ],
        'userLeftTopic' => [
            'subject' => 'User has left a topic you participate on',
            'user' => 'User',
            'intro' => 'has left a topic you participate on.',
            'actionText' => 'Open topic'
        ],
        'topicReminder' => [
            'actionText' => 'Open topic'
        ]

    ],
    'tasks' => 'Tasks',
    'discussions' => 'Discussions',
    'all' => 'All',
    'fromNewest' => 'Newest first',
    'fromOldest' => 'Oldest first',
    'resetFilters' => 'Reset filters',
    'reply' => 'Reply',
    'replyReference' => 'Reply to',
    'createDocumentation' => 'Create documentation',
    'createDocumentationFromComment' => 'Create documentation from comment',
    'loadNextComment' => 'Load following comment',
    'loadPreviousComment' => 'Load previous comment',
];
