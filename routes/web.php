<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


//Route::get('debug', 'DebugController@index')->name('debug');


// impersonate user, generate signed URL via tinker - URL::temporarySignedRoute('impersonate', now()->addMinutes(30), ['id' => $id])
Route::get('/impersonate', function() {
    Auth::logout();
    Auth::loginUsingId(request()->get('id'));
    return redirect(route('home'));
})->name('impersonate')->middleware('signed');



Route::get('locale/{locale}', function ($locale) {
    \Illuminate\Support\Facades\Session::put('locale', $locale);
    return back();
})->name('locale');



Route::group(['middleware' => ['auth', 'locale']], function () {

    Route::get('/', 'HomeController@index')->name('home');

    Route::get('profile', 'ProfileController@index')->name('profile');
    Route::get('notifications', 'ProfileController@notifications')->name('notifications');

    Route::get('topics', 'TopicController@index')->name('topics');
    Route::get('documentation', 'DocumentationController@index')->name('documentation');
    Route::get('categories', 'CategoryController@index')->name('categories');

    Route::get('topics/search', 'TopicController@search')->name('topics.search');
    Route::get('topics/create', 'TopicController@create')->name('topics.create');

    Route::post('topics/createTaskFromComment', 'TopicController@createTaskFromComment')->name('topics.createTaskFromComment');
    Route::post('documentation/createDocumentationFromComment', 'DocumentationController@createDocumentationFromComment')->name('documentation.createDocumentationFromComment');

    Route::get('search', 'SearchController@index')->name('search');
    Route::post('search', 'SearchController@search')->name('search.ai');

    Route::post('topics/store', 'TopicController@store')->name('topics.store');
    Route::put('topics/update/{topic}', 'TopicController@update')->name('topics.update');
    Route::get('topics/list', 'TopicController@list')->name('topics.list');
    Route::get('topics/{topic}/edit', 'TopicController@edit')->name('topics.edit');
    Route::get('topics/{topic}', 'TopicController@show')->name('topics.show');
    Route::get('topics/destroy/{topic}', 'TopicController@destroy')->name('topics.destroy');
    Route::get('topics/complete/{topic}', 'TopicController@complete')->name('topics.complete');
    Route::get('topics/reopen/{topic}', 'TopicController@reopen')->name('topics.reopen');
    Route::get('topics/leave/{topic}', 'TopicController@leave')->name('topics.leave');
    Route::get('/topics/{topic}/reminders/create', 'TopicController@reminderCreate')->name('topics.reminders.create');
    Route::post('topics/{topic}/reminders/store', 'TopicController@reminderStore')->name('topics.reminders.store');
    Route::get('reminders/{reminder}/edit', 'TopicController@reminderEdit')->name('topics.reminders.edit');
    Route::put('reminders/{reminder}/update', 'TopicController@reminderUpdate')->name('topics.reminders.update');
    Route::get('reminders/{reminder}/pause', 'TopicController@reminderPause')->name('topics.reminders.pause');
    Route::get('reminders/{reminder}/activate', 'TopicController@reminderActivate')->name('topics.reminders.activate');
    Route::get('reminders/{reminder}/delete', 'TopicController@reminderDelete')->name('topics.reminders.delete');
    Route::post('topics/edit/categories', 'TopicController@editCategories')->name('topics.editCategories');

    Route::post('topics/{topic}/comments/create', 'TopicCommentsController@create')->name('comments.create');
    Route::post('topics/{topic}/comments/documentation/create', 'TopicCommentsController@createDocumentation')->name('comments.documentation.create');
    Route::get('comments/{comment}/show', 'TopicCommentsController@show')->name('comments.show');
    Route::get('comments/{comment}/destroy', 'TopicCommentsController@destroy')->name('comments.destroy');
    Route::get('comments/{comment}/edit', 'TopicCommentsController@edit')->name('comments.edit');
    Route::put('comments/{comment}/update', 'TopicCommentsController@update')->name('comments.update');
    Route::post('comments/like', 'TopicCommentsController@like')->name('comments.like');
    Route::post('comments/unlike', 'TopicCommentsController@unlike')->name('comments.unlike');

    Route::post('comments/reminder', 'TopicCommentsController@sendReminder')->name('comments.reminder');

    Route::get('comments/{comment}/next', 'TopicCommentsController@loadNext')->name('comments.next');
    Route::get('comments/{comment}/previous', 'TopicCommentsController@loadPrevious')->name('comments.previous');

    Route::get('documentation/search', 'DocumentationController@search')->name('documentation.search');
    Route::get('documentation/create', 'DocumentationController@create')->name('documentation.create');
    Route::post('documentation/store', 'DocumentationController@store')->name('documentation.store');
    Route::get('documentation/list', 'DocumentationController@list')->name('documentation.list');
    Route::get('documentation/{documentation}', 'DocumentationController@show')->name('documentation.show');
    Route::get('documentation/{documentation}/destroy', 'DocumentationController@destroy')->name('documentation.destroy');
    Route::get('documentation/{documentation}/edit', 'DocumentationController@edit')->name('documentation.edit');
    Route::put('documentation/{documentation}/update', 'DocumentationController@update')->name('documentation.update');
    Route::post('documentation/edit/categories', 'DocumentationController@editCategories')->name('documentation.editCategories');

    Route::get('categories','CategoryController@index')->name('categories.list');
    Route::put('categories/update','CategoryController@update')->name('categories.update');
    Route::post('categories/store','CategoryController@store')->name('categories.store');
    Route::delete('categories/destroy','CategoryController@destroy')->name('categories.destroy');
    Route::post('categories/updateParent','CategoryController@updateParent')->name('categories.update.parent');

    Route::get('categories/uncategorized','CategoryController@uncategorized')->name('categories.uncategorized');
    Route::get('categories/list/uncategorized','CategoryController@listUncategorized')->name('categories.listUncategorized');

    Route::post('categories/assign/topics','CategoryController@assignTopics')->name('categories.assignTopics');
    Route::post('categories/assign/documentation','CategoryController@assignDocumentation')->name('categories.assignDocumentation');

    Route::get('dataTables/languageFile', 'ResourcesController@dataTablesLanguageFile')->name('dataTables.languageFile');

    Route::post('image/upload', 'ImageUploadController@upload')->name('image.upload');

    Route::get('resource/download/{id}', 'ResourcesController@download')->name('resource.download');

    Route::get('/storage/uploads/images/{filename}', 'ResourcesController@image')->name('resource.image');

});

// outside of web middleware (for emails)
Route::get('/image/email/{encrypted}', 'ResourcesController@imageForEmail')->name('resource.image.email');

// disable further registrations
Route::group(['middleware' => ['locale']], function () {
    Auth::routes(['register' => \Illuminate\Support\Facades\App::environment('local')]);
});






