{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "da8a7e04e84808546b3f8b4635cefb85", "packages": [{"name": "ankane/pgvector", "version": "v0.1.3", "source": {"type": "git", "url": "https://github.com/pgvector/pgvector-php.git", "reference": "ff407388899b5e9e268021448c106655846ede91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pgvector/pgvector-php/zipball/ff407388899b5e9e268021448c106655846ede91", "reference": "ff407388899b5e9e268021448c106655846ede91", "shasum": ""}, "require": {"php": ">= 7.4"}, "require-dev": {"illuminate/database": ">= 9.0", "phpunit/phpunit": "^9"}, "type": "library", "extra": {"laravel": {"providers": ["Pgvector\\Laravel\\PgvectorServiceProvider"]}}, "autoload": {"psr-4": {"Pgvector\\": "src/", "Pgvector\\Laravel\\": "src/laravel/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "pgvector support for PHP", "support": {"issues": "https://github.com/pgvector/pgvector-php/issues", "source": "https://github.com/pgvector/pgvector-php"}, "time": "2023-10-03T23:29:14+00:00"}, {"name": "brick/math", "version": "0.11.0", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/0ad82ce168c82ba30d1c01ec86116ab52f589478", "reference": "0ad82ce168c82ba30d1c01ec86116ab52f589478", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "5.0.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.11.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2023-01-15T23:15:59+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "f41715465d65213d644d3141a6a93081be5d3549"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/f41715465d65213d644d3141a6a93081be5d3549", "reference": "f41715465d65213d644d3141a6a93081be5d3549", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.2"}, "time": "2022-10-27T11:44:00+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "2.13.9", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.9"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-05-02T20:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "4f2d4f2836e7ec4e7a8625e75c6aa916004db931"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/4f2d4f2836e7ec4e7a8625e75c6aa916004db931", "reference": "4f2d4f2836e7ec4e7a8625e75c6aa916004db931", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.2"}, "time": "2023-09-27T20:04:15+00:00"}, {"name": "doctrine/event-manager", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/95aa4cb529f1e96576f3fda9f5705ada4056a520", "reference": "95aa4cb529f1e96576f3fda9f5705ada4056a520", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-10-12T20:51:15+00:00"}, {"name": "doctrine/inflector", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "f9301a5b2fb1216b2b08f02ba04dc45423db6bff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/f9301a5b2fb1216b2b08f02ba04dc45423db6bff", "reference": "f9301a5b2fb1216b2b08f02ba04dc45423db6bff", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.8"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2023-06-16T13:40:37+00:00"}, {"name": "doctrine/lexer", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-12-14T08:49:07+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "reference": "adfb1f505deb6384dc8b39804c5065dd3c8c8c0a", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpstan/phpstan-webmozart-assert": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.3.3"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2023-08-10T19:36:49+00:00"}, {"name": "egulias/email-validator", "version": "3.2.6", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "reference": "e5997fa97e8790cdae03a9cbd5e78e45e3c7bda7", "shasum": ""}, "require": {"doctrine/lexer": "^1.2|^2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.6"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-06-01T07:04:22+00:00"}, {"name": "elastic/transport", "version": "v8.8.0", "source": {"type": "git", "url": "**************:elastic/elastic-transport-php.git", "reference": "cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elastic-transport-php/zipball/cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b", "reference": "cdf9f63a16ec6bfb4c881ab89aa0e2a61fb7c20b", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "php": "^7.4 || ^8.0", "php-http/discovery": "^1.14", "php-http/httplug": "^2.3", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0 || ^2.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"nyholm/psr7": "^1.5", "php-http/mock-client": "^1.5", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Elastic\\Transport\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "HTTP transport PHP library for Elastic products", "keywords": ["PSR_17", "elastic", "http", "psr-18", "psr-7", "transport"], "time": "2023-11-08T10:51:51+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v8.11.0", "source": {"type": "git", "url": "**************:elastic/elasticsearch-php.git", "reference": "4c16dc67788afdec63294f072999594d0fd14ebb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/4c16dc67788afdec63294f072999594d0fd14ebb", "reference": "4c16dc67788afdec63294f072999594d0fd14ebb", "shasum": ""}, "require": {"elastic/transport": "^8.8", "guzzlehttp/guzzle": "^7.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.5", "nyholm/psr7": "^1.5", "php-http/message-factory": "^1.1", "php-http/mock-client": "^1.5", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5", "symfony/finder": "~4.0", "symfony/http-client": "^5.0|^6.0"}, "type": "library", "autoload": {"psr-4": {"Elastic\\Elasticsearch\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elastic", "elasticsearch", "search"], "time": "2023-11-11T09:01:22+00:00"}, {"name": "fideloper/proxy", "version": "4.4.2", "source": {"type": "git", "url": "https://github.com/fideloper/TrustedProxy.git", "reference": "a751f2bc86dd8e6cfef12dc0cbdada82f5a18750"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fideloper/TrustedProxy/zipball/a751f2bc86dd8e6cfef12dc0cbdada82f5a18750", "reference": "a751f2bc86dd8e6cfef12dc0cbdada82f5a18750", "shasum": ""}, "require": {"illuminate/contracts": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "^5.0|^6.0|^7.0|^8.0|^9.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "type": "library", "extra": {"laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "support": {"issues": "https://github.com/fideloper/TrustedProxy/issues", "source": "https://github.com/fideloper/TrustedProxy/tree/4.4.2"}, "time": "2022-02-09T13:33:34+00:00"}, {"name": "fruitcake/php-cors", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/fruitcake/php-cors.git", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b", "shasum": ""}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "homepage": "https://github.com/fruitcake/php-cors", "keywords": ["cors", "laravel", "symfony"], "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-10-12T05:21:21+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831", "reference": "672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.1"}, "require-dev": {"phpunit/phpunit": "^8.5.32 || ^9.6.3 || ^10.0.12"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2023-02-25T20:23:15+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/111166291a0f8130081195ac4556a5587d7f1b5d", "reference": "111166291a0f8130081195ac4556a5587d7f1b5d", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-08-03T15:11:55+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "be45764272e8873c72dbe3d2edcfdfcc3bc9f727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/be45764272e8873c72dbe3d2edcfdfcc3bc9f727", "reference": "be45764272e8873c72dbe3d2edcfdfcc3bc9f727", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-08-27T10:13:57+00:00"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/uri-template.git", "reference": "61bf437fc2197f587f6857d3ff903a24f1731b5d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/uri-template/zipball/61bf437fc2197f587f6857d3ff903a24f1731b5d", "reference": "61bf437fc2197f587f6857d3ff903a24f1731b5d", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.19 || ^9.5.8", "uri-template/tests": "1.0.0"}, "type": "library", "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "time": "2023-08-27T10:19:19+00:00"}, {"name": "intervention/image", "version": "2.7.2", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "04be355f8d6734c826045d02a1079ad658322dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/04be355f8d6734c826045d02a1079ad658322dad", "reference": "04be355f8d6734c826045d02a1079ad658322dad", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/2.7.2"}, "funding": [{"url": "https://paypal.me/interventionio", "type": "custom"}, {"url": "https://github.com/Intervention", "type": "github"}], "time": "2022-05-21T17:30:32+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.116", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "97e9fe30219e60092e107651abb379a38b342921"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/97e9fe30219e60092e107651abb379a38b342921", "reference": "97e9fe30219e60092e107651abb379a38b342921", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.116"}, "time": "2023-07-21T15:49:49+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "laravel/framework", "version": "v9.52.16", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "082345d76fc6a55b649572efe10b11b03e279d24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/082345d76fc6a55b649572efe10b11b03e279d24", "reference": "082345d76fc6a55b649572efe10b11b03e279d24", "shasum": ""}, "require": {"brick/math": "^0.9.3|^0.10.2|^0.11", "doctrine/inflector": "^2.0.5", "dragonmantank/cron-expression": "^3.3.2", "egulias/email-validator": "^3.2.1|^4.0", "ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-session": "*", "ext-tokenizer": "*", "fruitcake/php-cors": "^1.2", "guzzlehttp/uri-template": "^1.0", "laravel/serializable-closure": "^1.2.2", "league/commonmark": "^2.2.1", "league/flysystem": "^3.8.0", "monolog/monolog": "^2.0", "nesbot/carbon": "^2.62.1", "nunomaduro/termwind": "^1.13", "php": "^8.0.2", "psr/container": "^1.1.1|^2.0.1", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "ramsey/uuid": "^4.7", "symfony/console": "^6.0.9", "symfony/error-handler": "^6.0", "symfony/finder": "^6.0", "symfony/http-foundation": "^6.0", "symfony/http-kernel": "^6.0", "symfony/mailer": "^6.0", "symfony/mime": "^6.0", "symfony/process": "^6.0", "symfony/routing": "^6.0", "symfony/uid": "^6.0", "symfony/var-dumper": "^6.0", "tijsverkoyen/css-to-inline-styles": "^2.2.5", "vlucas/phpdotenv": "^5.4.1", "voku/portable-ascii": "^2.0"}, "conflict": {"tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.1|2.0", "psr/simple-cache-implementation": "1.0|2.0|3.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/collections": "self.version", "illuminate/conditionable": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/macroable": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"ably/ably-php": "^1.0", "aws/aws-sdk-php": "^3.235.5", "doctrine/dbal": "^2.13.3|^3.1.4", "ext-gmp": "*", "fakerphp/faker": "^1.21", "guzzlehttp/guzzle": "^7.5", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-path-prefixing": "^3.3", "league/flysystem-read-only": "^3.3", "league/flysystem-sftp-v3": "^3.0", "mockery/mockery": "^1.5.1", "orchestra/testbench-core": "^7.24", "pda/pheanstalk": "^4.0", "phpstan/phpdoc-parser": "^1.15", "phpstan/phpstan": "^1.4.7", "phpunit/phpunit": "^9.5.8", "predis/predis": "^1.1.9|^2.0.2", "symfony/cache": "^6.0", "symfony/http-client": "^6.0"}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage, and SES mail driver (^3.235.5).", "brianium/paratest": "Required to run tests in parallel (^6.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.13.3|^3.1.4).", "ext-apcu": "Required to use the APC cache driver.", "ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker and console signal trapping.", "ext-pdo": "Required to use all database features.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.14.3).", "guzzlehttp/guzzle": "Required to use the HTTP Client and the ping methods on schedules (^7.5).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-path-prefixing": "Required to use the scoped driver (^3.3).", "league/flysystem-read-only": "Required to use read-only disks (^3.3)", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "mockery/mockery": "Required to use mocking (^1.5.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^9.5.8).", "predis/predis": "Required to use the predis connector (^1.1.9|^2.0.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^6.0|^7.0).", "symfony/cache": "Required to PSR-6 cache bridge (^6.0).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.0).", "symfony/http-client": "Required to enable support for the Symfony API mail transports (^6.0).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^6.0).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^6.0).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/Illuminate/Collections/helpers.php", "src/Illuminate/Events/functions.php", "src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/", "Illuminate\\Support\\": ["src/Illuminate/Macroable/", "src/Illuminate/Collections/", "src/Illuminate/Conditionable/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2023-10-03T13:02:30+00:00"}, {"name": "laravel/serializable-closure", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "076fe2cf128bd54b4341cdc6d49b95b34e101e4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/076fe2cf128bd54b4341cdc6d49b95b34e101e4c", "reference": "076fe2cf128bd54b4341cdc6d49b95b34e101e4c", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"nesbot/carbon": "^2.61", "pestphp/pest": "^1.21.3", "phpstan/phpstan": "^1.8.2", "symfony/var-dumper": "^5.4.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2023-10-17T13:38:16+00:00"}, {"name": "laravel/telescope", "version": "v4.16.4", "source": {"type": "git", "url": "https://github.com/laravel/telescope.git", "reference": "5cb73363ef6d57e4087ccc380964c5120d34ec3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/telescope/zipball/5cb73363ef6d57e4087ccc380964c5120d34ec3c", "reference": "5cb73363ef6d57e4087ccc380964c5120d34ec3c", "shasum": ""}, "require": {"ext-json": "*", "laravel/framework": "^8.37|^9.0|^10.0", "php": "^8.0", "symfony/var-dumper": "^5.0|^6.0"}, "require-dev": {"ext-gd": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "laravel/octane": "^1.4", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}, "laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Telescope\\": "src/", "Laravel\\Telescope\\Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An elegant debug assistant for the Laravel framework.", "keywords": ["debugging", "laravel", "monitoring"], "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/v4.16.4"}, "time": "2023-09-25T13:48:25+00:00"}, {"name": "laravel/tinker", "version": "v2.8.2", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "b936d415b252b499e8c3b1f795cd4fc20f57e1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/b936d415b252b499e8c3b1f795cd4fc20f57e1f3", "reference": "b936d415b252b499e8c3b1f795cd4fc20f57e1f3", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.10.4|^0.11.1", "symfony/var-dumper": "^4.3.4|^5.0|^6.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.8|^9.3.3"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0|^10.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v2.8.2"}, "time": "2023-08-15T14:27:00+00:00"}, {"name": "laravel/ui", "version": "v3.4.6", "source": {"type": "git", "url": "https://github.com/laravel/ui.git", "reference": "65ec5c03f7fee2c8ecae785795b829a15be48c2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/ui/zipball/65ec5c03f7fee2c8ecae785795b829a15be48c2c", "reference": "65ec5c03f7fee2c8ecae785795b829a15be48c2c", "shasum": ""}, "require": {"illuminate/console": "^8.42|^9.0", "illuminate/filesystem": "^8.42|^9.0", "illuminate/support": "^8.82|^9.0", "illuminate/validation": "^8.42|^9.0", "php": "^7.3|^8.0"}, "require-dev": {"orchestra/testbench": "^6.23|^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}, "laravel": {"providers": ["Laravel\\Ui\\UiServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Ui\\": "src/", "Illuminate\\Foundation\\Auth\\": "auth-backend/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel UI utilities and presets.", "keywords": ["laravel", "ui"], "support": {"source": "https://github.com/laravel/ui/tree/v3.4.6"}, "time": "2022-05-20T13:38:08+00:00"}, {"name": "laravelcollective/html", "version": "v6.4.1", "source": {"type": "git", "url": "https://github.com/LaravelCollective/html.git", "reference": "64ddfdcaeeb8d332bd98bef442bef81e39c3910b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/LaravelCollective/html/zipball/64ddfdcaeeb8d332bd98bef442bef81e39c3910b", "reference": "64ddfdcaeeb8d332bd98bef442bef81e39c3910b", "shasum": ""}, "require": {"illuminate/http": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/routing": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/session": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/view": "^6.0|^7.0|^8.0|^9.0|^10.0", "php": ">=7.2.5"}, "require-dev": {"illuminate/database": "^6.0|^7.0|^8.0|^9.0|^10.0", "mockery/mockery": "~1.0", "phpunit/phpunit": "~8.5|^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}, "laravel": {"providers": ["Collective\\Html\\HtmlServiceProvider"], "aliases": {"Form": "Collective\\Html\\FormFacade", "Html": "Collective\\Html\\HtmlFacade"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Collective\\Html\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTML and Form Builders for the Laravel Framework", "homepage": "https://laravelcollective.com", "support": {"issues": "https://github.com/LaravelCollective/html/issues", "source": "https://github.com/LaravelCollective/html"}, "abandoned": "spatie/laravel-html", "time": "2023-04-25T02:46:11+00:00"}, {"name": "laravolt/avatar", "version": "4.1.7", "source": {"type": "git", "url": "https://github.com/laravolt/avatar.git", "reference": "2c11878524e19032793effa67f09df0682e5775b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravolt/avatar/zipball/2c11878524e19032793effa67f09df0682e5775b", "reference": "2c11878524e19032793effa67f09df0682e5775b", "shasum": ""}, "require": {"illuminate/cache": "^6.0|^7.0|^8.0|^9.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0", "intervention/image": "^2.5", "php": ">=7.3"}, "require-dev": {"mockery/mockery": "~1.3", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}, "laravel": {"providers": ["Laravolt\\Avatar\\ServiceProvider"], "aliases": {"Avatar": "Laravolt\\Avatar\\Facade"}}}, "autoload": {"psr-4": {"Laravolt\\Avatar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://laravolt.dev", "role": "Developer"}], "description": "Turn name, email, and any other string into initial-based avatar or gravatar.", "homepage": "https://github.com/laravolt/avatar", "keywords": ["avatar", "gravatar", "laravel", "laravolt"], "support": {"issues": "https://github.com/laravolt/avatar/issues", "source": "https://github.com/laravolt/avatar/tree/4.1.7"}, "funding": [{"url": "https://paypal.me/bayuhendra", "type": "custom"}, {"url": "https://ko-fi.com/bayuhendra", "type": "ko_fi"}, {"url": "https://www.patreon.com/uyab", "type": "patreon"}], "time": "2022-03-07T23:07:16+00:00"}, {"name": "league/commonmark", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "3669d6d5f7a47a93c08ddff335e6d945481a1dd5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/3669d6d5f7a47a93c08ddff335e6d945481a1dd5", "reference": "3669d6d5f7a47a93c08ddff335e6d945481a1dd5", "shasum": ""}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.30.0", "commonmark/commonmark.js": "0.30.0", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}}, "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2023-08-30T16:55:00+00:00"}, {"name": "league/config", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2022-12-11T20:36:23+00:00"}, {"name": "league/flysystem", "version": "3.18.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "015633a05aee22490495159237a5944091d8281e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/015633a05aee22490495159237a5944091d8281e", "reference": "015633a05aee22490495159237a5944091d8281e", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.220.0", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "microsoft/azure-storage-blob": "^1.1", "phpseclib/phpseclib": "^3.0.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.3.1"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.18.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2023-10-20T17:59:40+00:00"}, {"name": "league/flysystem-local", "version": "3.18.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "e7381ef7643f658b87efb7dbe98fe538fb1bbf32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/e7381ef7643f658b87efb7dbe98fe538fb1bbf32", "reference": "e7381ef7643f658b87efb7dbe98fe538fb1bbf32", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"issues": "https://github.com/thephpleague/flysystem-local/issues", "source": "https://github.com/thephpleague/flysystem-local/tree/3.18.0"}, "funding": [{"url": "https://ecologi.com/frankdejonge", "type": "custom"}, {"url": "https://github.com/frankdejonge", "type": "github"}], "time": "2023-10-19T20:07:13+00:00"}, {"name": "league/fractal", "version": "0.20.1", "source": {"type": "git", "url": "https://github.com/thephpleague/fractal.git", "reference": "8b9d39b67624db9195c06f9c1ffd0355151eaf62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/fractal/zipball/8b9d39b67624db9195c06f9c1ffd0355151eaf62", "reference": "8b9d39b67624db9195c06f9c1ffd0355151eaf62", "shasum": ""}, "require": {"php": ">=7.4"}, "require-dev": {"doctrine/orm": "^2.5", "illuminate/contracts": "~5.0", "mockery/mockery": "^1.3", "pagerfanta/pagerfanta": "~1.0.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "~3.4", "vimeo/psalm": "^4.22", "zendframework/zend-paginator": "~2.3"}, "suggest": {"illuminate/pagination": "The Illuminate Pagination component.", "pagerfanta/pagerfanta": "Pagerfant<PERSON>", "zendframework/zend-paginator": "Zend Framework Paginator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.20.x-dev"}}, "autoload": {"psr-4": {"League\\Fractal\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://philsturgeon.uk/", "role": "Developer"}], "description": "Handle the output of complex data structures ready for API output.", "homepage": "http://fractal.thephpleague.com/", "keywords": ["api", "json", "league", "rest"], "support": {"issues": "https://github.com/thephpleague/fractal/issues", "source": "https://github.com/thephpleague/fractal/tree/0.20.1"}, "time": "2022-04-11T12:47:17+00:00"}, {"name": "league/mime-type-detection", "version": "1.14.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "b6a5854368533df0295c5761a0253656a2e52d9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/b6a5854368533df0295c5761a0253656a2e52d9e", "reference": "b6a5854368533df0295c5761a0253656a2e52d9e", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.14.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2023-10-17T14:13:20+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.41", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "fc9cccd4d3706d5a7537b562b59cc18f9e4c0cb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/fc9cccd4d3706d5a7537b562b59cc18f9e4c0cb1", "reference": "fc9cccd4d3706d5a7537b562b59cc18f9e4c0cb1", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.41"}, "time": "2022-11-08T18:31:26+00:00"}, {"name": "monolog/monolog", "version": "2.9.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "f259e2b15fb95494c83f52d3caad003bbf5ffaa1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/f259e2b15fb95494c83f52d3caad003bbf5ffaa1", "reference": "f259e2b15fb95494c83f52d3caad003bbf5ffaa1", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2@dev", "guzzlehttp/guzzle": "^7.4", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "phpspec/prophecy": "^1.15", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5.14", "predis/predis": "^1.1 || ^2.0", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": "^7", "swiftmailer/swiftmailer": "^5.3|^6.0", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.9.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2023-02-06T13:44:46+00:00"}, {"name": "nesbot/carbon", "version": "2.71.0", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "98276233188583f2ff845a0f992a235472d9466a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/98276233188583f2ff845a0f992a235472d9466a", "reference": "98276233188583f2ff845a0f992a235472d9466a", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4", "doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "*", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.x-dev", "dev-master": "2.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2023-09-25T11:31:05+00:00"}, {"name": "nette/schema", "version": "v1.2.5", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/0462f0166e823aad657c9224d0f849ecac1ba10a", "reference": "0462f0166e823aad657c9224d0f849ecac1ba10a", "shasum": ""}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": "7.1 - 8.3"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.5"}, "time": "2023-10-05T20:37:59+00:00"}, {"name": "nette/utils", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "cead6637226456b35e1175cc53797dd585d85545"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/cead6637226456b35e1175cc53797dd585d85545", "reference": "cead6637226456b35e1175cc53797dd585d85545", "shasum": ""}, "require": {"php": ">=8.0 <8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.2"}, "time": "2023-09-19T11:58:07+00:00"}, {"name": "nikic/php-parser", "version": "v4.17.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "reference": "a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.17.1"}, "time": "2023-08-13T19:53:39+00:00"}, {"name": "nunomaduro/termwind", "version": "v1.15.1", "source": {"type": "git", "url": "https://github.com/nunomaduro/termwind.git", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "reference": "8ab0b32c8caa4a2e09700ea32925441385e4a5dc", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.0", "symfony/console": "^5.3.0|^6.0.0"}, "require-dev": {"ergebnis/phpstan-rules": "^1.0.", "illuminate/console": "^8.0|^9.0", "illuminate/support": "^8.0|^9.0", "laravel/pint": "^1.0.0", "pestphp/pest": "^1.21.0", "pestphp/pest-plugin-mock": "^1.0", "phpstan/phpstan": "^1.4.6", "phpstan/phpstan-strict-rules": "^1.1.0", "symfony/var-dumper": "^5.2.7|^6.0.0", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "type": "library", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Its like Tailwind CSS, but for the console.", "keywords": ["cli", "console", "css", "package", "php", "style"], "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.15.1"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "time": "2023-02-08T01:06:31+00:00"}, {"name": "orhanerday/open-ai", "version": "5.0", "source": {"type": "git", "url": "https://github.com/orhanerday/open-ai.git", "reference": "48c94a922318a33dbc15436e8bc086f00d869a4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/orhanerday/open-ai/zipball/48c94a922318a33dbc15436e8bc086f00d869a4e", "reference": "48c94a922318a33dbc15436e8bc086f00d869a4e", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=7.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "pestphp/pest": "^1.20", "spatie/ray": "^1.28"}, "type": "library", "autoload": {"psr-4": {"Orhanerday\\OpenAi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "OpenAI GPT-3 Api Client in PHP", "homepage": "https://github.com/orhanerday/open-ai", "keywords": ["open-ai", "<PERSON><PERSON><PERSON><PERSON>"], "support": {"issues": "https://github.com/orhanerday/open-ai/issues", "source": "https://github.com/orhanerday/open-ai/tree/5.0"}, "funding": [{"url": "https://github.com/orhanerday", "type": "github"}], "time": "2023-11-30T06:47:07+00:00"}, {"name": "php-http/discovery", "version": "1.19.2", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "61e1a1eb69c92741f5896d9e05fb8e9d7e8bb0cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/61e1a1eb69c92741f5896d9e05fb8e9d7e8bb0cb", "reference": "61e1a1eb69c92741f5896d9e05fb8e9d7e8bb0cb", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "symfony/phpunit-bridge": "^6.2"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.19.2"}, "time": "2023-11-30T16:49:05+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/promise", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "2916a606d3b390f4e9e8e2b8dd68581508be0f07"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/2916a606d3b390f4e9e8e2b8dd68581508be0f07", "reference": "2916a606d3b390f4e9e8e2b8dd68581508be0f07", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.0"}, "time": "2024-01-04T18:49:48+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "dd3a383e599f49777d8b628dadbb90cae435b87e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/dd3a383e599f49777d8b628dadbb90cae435b87e", "reference": "dd3a383e599f49777d8b628dadbb90cae435b87e", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.32 || ^9.6.3 || ^10.0.12"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2023-02-25T19:38:58+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "psy/psysh", "version": "v0.11.22", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "128fa1b608be651999ed9789c95e6e2a31b5802b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/128fa1b608be651999ed9789c95e6e2a31b5802b", "reference": "128fa1b608be651999ed9789c95e6e2a31b5802b", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^4.0 || ^3.1", "php": "^8.0 || ^7.0.8", "symfony/console": "^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-0.11": "0.11.x-dev"}, "bamarni-bin": {"bin-links": false, "forward-command": false}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.11.22"}, "time": "2023-10-14T21:56:36+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/ad7475d1c9e70b190ecffc58f2d989416af339b4", "reference": "ad7475d1c9e70b190ecffc58f2d989416af339b4", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "symfony/polyfill-php81": "^1.23"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.28.3", "fakerphp/faker": "^1.21", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^1.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpcsstandards/phpcsutils": "^1.0.0-rc1", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.9", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5", "psalm/plugin-mockery": "^1.1", "psalm/plugin-phpunit": "^0.18.4", "ramsey/coding-standard": "^2.0.3", "ramsey/conventional-commits": "^1.3", "vimeo/psalm": "^5.4"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/1.3.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/collection", "type": "tidelift"}], "time": "2022-12-27T19:12:24+00:00"}, {"name": "ramsey/uuid", "version": "4.7.4", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "60a4c63ab724854332900504274f6150ff26d286"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/60a4c63ab724854332900504274f6150ff26d286", "reference": "60a4c63ab724854332900504274f6150ff26d286", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11", "ext-json": "*", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.10", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.8", "ergebnis/composer-normalize": "^2.15", "mockery/mockery": "^1.3", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.2", "php-mock/php-mock-mockery": "^1.3", "php-parallel-lint/php-parallel-lint": "^1.1", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^8.5 || ^9", "ramsey/composer-repl": "^1.4", "slevomat/coding-standard": "^8.4", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.7.4"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2023-04-15T23:01:58+00:00"}, {"name": "spatie/db-dumper", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/spatie/db-dumper.git", "reference": "bbd5ae0f331d47e6534eb307e256c11a65c8e24a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/db-dumper/zipball/bbd5ae0f331d47e6534eb307e256c11a65c8e24a", "reference": "bbd5ae0f331d47e6534eb307e256c11a65c8e24a", "shasum": ""}, "require": {"php": "^8.0", "symfony/process": "^5.0|^6.0"}, "require-dev": {"pestphp/pest": "^1.22"}, "type": "library", "autoload": {"psr-4": {"Spatie\\DbDumper\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Dump databases", "homepage": "https://github.com/spatie/db-dumper", "keywords": ["database", "db-dumper", "dump", "mysqldump", "spatie"], "support": {"source": "https://github.com/spatie/db-dumper/tree/3.4.0"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2023-06-27T08:34:52+00:00"}, {"name": "spatie/laravel-backup", "version": "8.2.0", "source": {"type": "git", "url": "https://github.com/spatie/laravel-backup.git", "reference": "2d37340b917d87590baf86011b56ee12d1657891"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-backup/zipball/2d37340b917d87590baf86011b56ee12d1657891", "reference": "2d37340b917d87590baf86011b56ee12d1657891", "shasum": ""}, "require": {"ext-zip": "^1.14.0", "illuminate/console": "^9.0|^10.0", "illuminate/contracts": "^9.0|^10.0", "illuminate/events": "^9.0|^10.0", "illuminate/filesystem": "^9.0|^10.0", "illuminate/notifications": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "league/flysystem": "^3.0", "php": "^8.0", "spatie/db-dumper": "^3.0", "spatie/laravel-package-tools": "^1.6.2", "spatie/laravel-signal-aware-command": "^1.2", "spatie/temporary-directory": "^2.0", "symfony/console": "^6.0", "symfony/finder": "^6.0"}, "require-dev": {"composer-runtime-api": "^2.0", "ext-pcntl": "*", "laravel/slack-notification-channel": "^2.5", "league/flysystem-aws-s3-v3": "^2.0|^3.0", "mockery/mockery": "^1.4", "nunomaduro/larastan": "^2.1", "orchestra/testbench": "^7.0|^8.0", "pestphp/pest": "^1.20", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.1"}, "suggest": {"laravel/slack-notification-channel": "Required for sending notifications via Slack"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\Backup\\BackupServiceProvider"]}}, "autoload": {"files": ["src/Helpers/functions.php"], "psr-4": {"Spatie\\Backup\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A Laravel package to backup your application", "homepage": "https://github.com/spatie/laravel-backup", "keywords": ["backup", "database", "laravel-backup", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-backup/issues", "source": "https://github.com/spatie/laravel-backup/tree/8.2.0"}, "funding": [{"url": "https://github.com/sponsors/spatie", "type": "github"}, {"url": "https://spatie.be/open-source/support-us", "type": "other"}], "time": "2023-08-07T15:31:34+00:00"}, {"name": "spatie/laravel-package-tools", "version": "1.16.1", "source": {"type": "git", "url": "https://github.com/spatie/laravel-package-tools.git", "reference": "cc7c991555a37f9fa6b814aa03af73f88026a83d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-package-tools/zipball/cc7c991555a37f9fa6b814aa03af73f88026a83d", "reference": "cc7c991555a37f9fa6b814aa03af73f88026a83d", "shasum": ""}, "require": {"illuminate/contracts": "^9.28|^10.0", "php": "^8.0"}, "require-dev": {"mockery/mockery": "^1.5", "orchestra/testbench": "^7.7|^8.0", "pestphp/pest": "^1.22", "phpunit/phpunit": "^9.5.24", "spatie/pest-plugin-test-time": "^1.1"}, "type": "library", "autoload": {"psr-4": {"Spatie\\LaravelPackageTools\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Tools for creating Laravel packages", "homepage": "https://github.com/spatie/laravel-package-tools", "keywords": ["laravel-package-tools", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-package-tools/issues", "source": "https://github.com/spatie/laravel-package-tools/tree/1.16.1"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2023-08-23T09:04:39+00:00"}, {"name": "spatie/laravel-signal-aware-command", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/spatie/laravel-signal-aware-command.git", "reference": "46cda09a85aef3fd47fb73ddc7081f963e255571"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-signal-aware-command/zipball/46cda09a85aef3fd47fb73ddc7081f963e255571", "reference": "46cda09a85aef3fd47fb73ddc7081f963e255571", "shasum": ""}, "require": {"illuminate/contracts": "^8.35|^9.0|^10.0", "php": "^8.0", "spatie/laravel-package-tools": "^1.4.3"}, "require-dev": {"brianium/paratest": "^6.2", "ext-pcntl": "*", "nunomaduro/collision": "^5.3|^6.0", "orchestra/testbench": "^6.16|^7.0|^8.0", "pestphp/pest-plugin-laravel": "^1.3", "phpunit/phpunit": "^9.5", "spatie/laravel-ray": "^1.17"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\SignalAwareCommand\\SignalAwareCommandServiceProvider"], "aliases": {"Signal": "Spatie\\SignalAwareCommand\\Facades\\Signal"}}}, "autoload": {"psr-4": {"Spatie\\SignalAwareCommand\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Handle signals in artisan commands", "homepage": "https://github.com/spatie/laravel-signal-aware-command", "keywords": ["laravel", "laravel-signal-aware-command", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-signal-aware-command/issues", "source": "https://github.com/spatie/laravel-signal-aware-command/tree/1.3.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2023-01-14T21:10:59+00:00"}, {"name": "spatie/temporary-directory", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/spatie/temporary-directory.git", "reference": "efc258c9f4da28f0c7661765b8393e4ccee3d19c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/temporary-directory/zipball/efc258c9f4da28f0c7661765b8393e4ccee3d19c", "reference": "efc258c9f4da28f0c7661765b8393e4ccee3d19c", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Spatie\\TemporaryDirectory\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Easily create, use and destroy temporary directories", "homepage": "https://github.com/spatie/temporary-directory", "keywords": ["php", "spatie", "temporary-directory"], "support": {"issues": "https://github.com/spatie/temporary-directory/issues", "source": "https://github.com/spatie/temporary-directory/tree/2.2.0"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2023-09-25T07:13:36+00:00"}, {"name": "symfony/console", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "c3ebc83d031b71c39da318ca8b7a07ecc67507ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c3ebc83d031b71c39da318ca8b7a07ecc67507ed", "reference": "c3ebc83d031b71c39da318ca8b7a07ecc67507ed", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.4|^6.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/lock": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/var-dumper": "^5.4|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/css-selector", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "f1d00bddb83a4cb2138564b2150001cb6ce272b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/f1d00bddb83a4cb2138564b2150001cb6ce272b1", "reference": "f1d00bddb83a4cb2138564b2150001cb6ce272b1", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "reference": "26954b3d62a6c5fd0ea8a2a00c0353a14978d05c", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/error-handler", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "c7df52182f43a68522756ac31a532dd5b1e6db67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/c7df52182f43a68522756ac31a532dd5b1e6db67", "reference": "c7df52182f43a68522756ac31a532dd5b1e6db67", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0"}, "require-dev": {"symfony/deprecation-contracts": "^2.1|^3", "symfony/http-kernel": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/event-dispatcher", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "2eaf8e63bc5b8cefabd4a800157f0d0c094f677a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "reference": "2eaf8e63bc5b8cefabd4a800157f0d0c094f677a", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/event-dispatcher-contracts": "^2|^3"}, "conflict": {"symfony/dependency-injection": "<5.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/error-handler": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^5.4|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/7bc61cc2db649b4637d331240c5346dcc7708051", "reference": "7bc61cc2db649b4637d331240c5346dcc7708051", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/finder", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "5cc9cac6586fc0c28cd173780ca696e419fefa11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/5cc9cac6586fc0c28cd173780ca696e419fefa11", "reference": "5cc9cac6586fc0c28cd173780ca696e419fefa11", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-20T17:44:14+00:00"}, {"name": "symfony/http-foundation", "version": "v6.0.20", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "e16b2676a4b3b1fa12378a20b29c364feda2a8d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/e16b2676a4b3b1fa12378a20b29c364feda2a8d6", "reference": "e16b2676a4b3b1fa12378a20b29c364feda2a8d6", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"predis/predis": "~1.0", "symfony/cache": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4", "symfony/mime": "^5.4|^6.0", "symfony/rate-limiter": "^5.2|^6.0"}, "suggest": {"symfony/mime": "To use the file extension guesser"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.0.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-30T15:41:07+00:00"}, {"name": "symfony/http-kernel", "version": "v6.0.20", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "6dc70833fd0ef5e861e17c7854c12d7d86679349"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/6dc70833fd0ef5e861e17c7854c12d7d86679349", "reference": "6dc70833fd0ef5e861e17c7854c12d7d86679349", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/error-handler": "^5.4|^6.0", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/twig-bridge": "<5.4", "symfony/validator": "<5.4", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/css-selector": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/dom-crawler": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2|^3", "symfony/process": "^5.4|^6.0", "symfony/routing": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0", "symfony/translation": "^5.4|^6.0", "symfony/translation-contracts": "^1.1|^2|^3", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.0.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-02-01T08:22:55+00:00"}, {"name": "symfony/mailer", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "cd60799210c488f545ddde2444dc1aa548322872"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/cd60799210c488f545ddde2444dc1aa548322872", "reference": "cd60799210c488f545ddde2444dc1aa548322872", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.0.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"symfony/http-kernel": "<5.4"}, "require-dev": {"symfony/http-client-contracts": "^1.1|^2|^3", "symfony/messenger": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-11T11:50:03+00:00"}, {"name": "symfony/mime", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "d7052547a0070cbeadd474e172b527a00d657301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/d7052547a0070cbeadd474e172b527a00d657301", "reference": "d7052547a0070cbeadd474e172b527a00d657301", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<5.4.14|>=6.0,<6.0.14|>=6.1,<6.1.6"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/serializer": "^5.4.14|~6.0.14|^6.1.6"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-11T11:50:03+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "reference": "ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "875e90aeea2777b6f135677f618529449334a612"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/875e90aeea2777b6f135677f618529449334a612", "reference": "875e90aeea2777b6f135677f618529449334a612", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "ecaafce9f77234a6a449d29e49267ba10499116d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/ecaafce9f77234a6a449d29e49267ba10499116d", "reference": "ecaafce9f77234a6a449d29e49267ba10499116d", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:30:37+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "reference": "8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "42292d99c55abe617799667f454222c54c60e229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/42292d99c55abe617799667f454222c54c60e229", "reference": "42292d99c55abe617799667f454222c54c60e229", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-07-28T09:04:16+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "70f4aebd92afca2f865444d30a4d2151c13c3179"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/70f4aebd92afca2f865444d30a4d2151c13c3179", "reference": "70f4aebd92afca2f865444d30a4d2151c13c3179", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "reference": "6caa57379c4aec19c0a12a38b59b26487dcfe4b5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "7581cd600fa9fd681b797d00b02f068e2f13263b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/7581cd600fa9fd681b797d00b02f068e2f13263b", "reference": "7581cd600fa9fd681b797d00b02f068e2f13263b", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.28.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "9c44518a5aff8da565c8a55dbe85d2769e6f630e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/9c44518a5aff8da565c8a55dbe85d2769e6f630e", "reference": "9c44518a5aff8da565c8a55dbe85d2769e6f630e", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.28-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.28.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-26T09:26:14+00:00"}, {"name": "symfony/process", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "2114fd60f26a296cc403a7939ab91478475a33d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/2114fd60f26a296cc403a7939ab91478475a33d4", "reference": "2114fd60f26a296cc403a7939ab91478475a33d4", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/routing", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "e56ca9b41c1ec447193474cd86ad7c0b547755ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/e56ca9b41c1ec447193474cd86ad7c0b547755ac", "reference": "e56ca9b41c1ec447193474cd86ad7c0b547755ac", "shasum": ""}, "require": {"php": ">=8.0.2"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/service-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "reference": "d78d39c1599bd1188b8e26bb341da52c3c6d8a66", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/container": "^2.0"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:58+00:00"}, {"name": "symfony/string", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "d9e72497367c23e08bf94176d2be45b00a9d232a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/d9e72497367c23e08bf94176d2be45b00a9d232a", "reference": "d9e72497367c23e08bf94176d2be45b00a9d232a", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/translation-contracts": "^2.0|^3.0", "symfony/var-exporter": "^5.4|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/translation", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "reference": "9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.3|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client-contracts": "^1.1|^2.0|^3.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/service-contracts": "^1.1.2|^2|^3", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/acbfbb274e730e5a0236f619b6168d9dedb3e282", "reference": "acbfbb274e730e5a0236f619b6168d9dedb3e282", "shasum": ""}, "require": {"php": ">=8.0.2"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.0.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T17:10:44+00:00"}, {"name": "symfony/uid", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "6499e28b0ac9f2aa3151e11845bdb5cd21e6bb9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/6499e28b0ac9f2aa3151e11845bdb5cd21e6bb9d", "reference": "6499e28b0ac9f2aa3151e11845bdb5cd21e6bb9d", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:36:10+00:00"}, {"name": "symfony/var-dumper", "version": "v6.0.19", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "eb980457fa6899840fe1687e8627a03a7d8a3d52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/eb980457fa6899840fe1687e8627a03a7d8a3d52", "reference": "eb980457fa6899840fe1687e8627a03a7d8a3d52", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"phpunit/phpunit": "<5.4.3", "symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/uid": "^5.4|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.0.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-20T17:44:14+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.6", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "c42125b83a4fa63b187fdf29f9c93cb7733da30c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/c42125b83a4fa63b187fdf29f9c93cb7733da30c", "reference": "c42125b83a4fa63b187fdf29f9c93cb7733da30c", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5 || ^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.6"}, "time": "2023-01-03T09:29:04+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "reference": "1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.0.2", "php": "^7.1.3 || ^8.0", "phpoption/phpoption": "^1.8", "symfony/polyfill-ctype": "^1.23", "symfony/polyfill-mbstring": "^1.23.1", "symfony/polyfill-php80": "^1.23.1"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-filter": "*", "phpunit/phpunit": "^7.5.20 || ^8.5.30 || ^9.5.25"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": true}, "branch-alias": {"dev-master": "5.5-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.5.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2022-10-16T01:01:54+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b56450eed252f6801410d810c8e1727224ae0743"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b56450eed252f6801410d810c8e1727224ae0743", "reference": "b56450eed252f6801410d810c8e1727224ae0743", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.1"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2022-03-08T17:03:00+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}, {"name": "yajra/laravel-datatables", "version": "9.x-dev", "source": {"type": "git", "url": "https://github.com/yajra/datatables.git", "reference": "38665252f324af6b93bb52b4a832758df1fa8130"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yajra/datatables/zipball/38665252f324af6b93bb52b4a832758df1fa8130", "reference": "38665252f324af6b93bb52b4a832758df1fa8130", "shasum": ""}, "require": {"php": "^8.0.2", "yajra/laravel-datatables-buttons": "9.*", "yajra/laravel-datatables-editor": "1.*", "yajra/laravel-datatables-fractal": "9.*", "yajra/laravel-datatables-html": "9.*", "yajra/laravel-datatables-oracle": "10.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.0-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel DataTables Complete Package.", "keywords": ["datatables", "j<PERSON>y", "laravel"], "support": {"issues": "https://github.com/yajra/datatables/issues", "source": "https://github.com/yajra/datatables/tree/master"}, "funding": [{"url": "https://www.paypal.me/yajra", "type": "custom"}, {"url": "https://github.com/yajra", "type": "github"}, {"url": "https://www.patreon.com/yajra", "type": "patreon"}], "time": "2022-10-08T01:55:26+00:00"}, {"name": "yajra/laravel-datatables-buttons", "version": "v9.1.4", "source": {"type": "git", "url": "https://github.com/yajra/laravel-datatables-buttons.git", "reference": "afc40285b09b0960180b17b96c5429b4be772143"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yajra/laravel-datatables-buttons/zipball/afc40285b09b0960180b17b96c5429b4be772143", "reference": "afc40285b09b0960180b17b96c5429b4be772143", "shasum": ""}, "require": {"illuminate/console": "*", "php": "^8.0.2", "yajra/laravel-datatables-html": "9.*", "yajra/laravel-datatables-oracle": "10.*"}, "require-dev": {"barryvdh/laravel-snappy": "^1.0", "maatwebsite/excel": "^3.1.40", "nunomaduro/larastan": "2.1.*", "orchestra/testbench": "^7.3", "rap2hpoutre/fast-excel": "^3.2"}, "suggest": {"barryvdh/laravel-snappy": "Allows exporting of dataTable to PDF using the print view.", "dompdf/dompdf": "Allows exporting of dataTable to PDF using the DomPDF.", "maatwebsite/excel": "Exporting of dataTables (excel, csv and PDF) using maatwebsite package.", "rap2hpoutre/fast-excel": "Faster exporting of dataTables using fast-excel package."}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.0-dev"}, "laravel": {"providers": ["Yajra\\DataTables\\ButtonsServiceProvider"]}}, "autoload": {"psr-4": {"Yajra\\DataTables\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel DataTables Buttons Plugin.", "keywords": ["buttons", "datatables", "j<PERSON>y", "laravel"], "support": {"issues": "https://github.com/yajra/laravel-datatables-buttons/issues", "source": "https://github.com/yajra/laravel-datatables-buttons/tree/v9.1.4"}, "funding": [{"url": "https://www.paypal.me/yajra", "type": "custom"}, {"url": "https://github.com/yajra", "type": "github"}, {"url": "https://www.patreon.com/yajra", "type": "patreon"}], "time": "2023-02-20T06:11:01+00:00"}, {"name": "yajra/laravel-datatables-editor", "version": "v1.25.4", "source": {"type": "git", "url": "https://github.com/yajra/laravel-datatables-editor.git", "reference": "23962356700d6b31f49bb119665b13e87303e13f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yajra/laravel-datatables-editor/zipball/23962356700d6b31f49bb119665b13e87303e13f", "reference": "23962356700d6b31f49bb119665b13e87303e13f", "shasum": ""}, "require": {"illuminate/console": "*", "illuminate/database": "*", "illuminate/http": "*", "illuminate/validation": "*", "php": ">=7.0"}, "require-dev": {"orchestra/testbench": "~3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Yajra\\DataTables\\EditorServiceProvider"]}}, "autoload": {"psr-4": {"Yajra\\DataTables\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel DataTables Editor plugin for Laravel 5.5+.", "keywords": ["JS", "datatables", "editor", "html", "j<PERSON>y", "laravel"], "support": {"issues": "https://github.com/yajra/laravel-datatables-editor/issues", "source": "https://github.com/yajra/laravel-datatables-editor/tree/v1.25.4"}, "funding": [{"url": "https://www.paypal.me/yajra", "type": "custom"}, {"url": "https://github.com/yajra", "type": "github"}, {"url": "https://www.patreon.com/yajra", "type": "patreon"}], "time": "2023-02-21T06:57:59+00:00"}, {"name": "yajra/laravel-datatables-fractal", "version": "v9.1.0", "source": {"type": "git", "url": "https://github.com/yajra/laravel-datatables-fractal.git", "reference": "4b313041247108650c9ce5deb678defb7e00e794"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yajra/laravel-datatables-fractal/zipball/4b313041247108650c9ce5deb678defb7e00e794", "reference": "4b313041247108650c9ce5deb678defb7e00e794", "shasum": ""}, "require": {"league/fractal": "^0.20.1", "php": ">=8.0.2", "yajra/laravel-datatables-oracle": "^10.0"}, "require-dev": {"nunomaduro/larastan": "^2.1", "orchestra/testbench": "^7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.0-dev"}, "laravel": {"providers": ["Yajra\\DataTables\\FractalServiceProvider"]}}, "autoload": {"psr-4": {"Yajra\\DataTables\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel DataTables Fractal Plugin.", "keywords": ["api", "datatables", "fractal", "laravel"], "support": {"issues": "https://github.com/yajra/laravel-datatables-fractal/issues", "source": "https://github.com/yajra/laravel-datatables-fractal/tree/v9.1.0"}, "time": "2022-06-20T01:43:31+00:00"}, {"name": "yajra/laravel-datatables-html", "version": "v9.4.0", "source": {"type": "git", "url": "https://github.com/yajra/laravel-datatables-html.git", "reference": "cec3e77746ff68f6f51e22250061b59d4ec1311c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yajra/laravel-datatables-html/zipball/cec3e77746ff68f6f51e22250061b59d4ec1311c", "reference": "cec3e77746ff68f6f51e22250061b59d4ec1311c", "shasum": ""}, "require": {"ext-json": "*", "laravelcollective/html": "^6.3.0", "php": "^8.0.2", "yajra/laravel-datatables-oracle": "^10.0"}, "require-dev": {"nunomaduro/larastan": "^2.1", "orchestra/testbench": "^7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.0-dev"}, "laravel": {"providers": ["Yajra\\DataTables\\HtmlServiceProvider"]}}, "autoload": {"psr-4": {"Yajra\\DataTables\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel DataTables HTML builder plugin for Laravel 5.4+.", "keywords": ["JS", "datatables", "html", "j<PERSON>y", "laravel"], "support": {"issues": "https://github.com/yajra/laravel-datatables-html/issues", "source": "https://github.com/yajra/laravel-datatables-html/tree/v9.4.0"}, "funding": [{"url": "https://www.paypal.me/yajra", "type": "custom"}, {"url": "https://github.com/yajra", "type": "github"}, {"url": "https://www.patreon.com/yajra", "type": "patreon"}], "time": "2023-02-20T07:44:43+00:00"}, {"name": "yajra/laravel-datatables-oracle", "version": "v10.10.0", "source": {"type": "git", "url": "https://github.com/yajra/laravel-datatables.git", "reference": "b4943a3ee422e0a390d85a4cb01dee9360137b61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yajra/laravel-datatables/zipball/b4943a3ee422e0a390d85a4cb01dee9360137b61", "reference": "b4943a3ee422e0a390d85a4cb01dee9360137b61", "shasum": ""}, "require": {"illuminate/database": "^9|^10", "illuminate/filesystem": "^9|^10", "illuminate/http": "^9|^10", "illuminate/support": "^9|^10", "illuminate/view": "^9|^10", "php": "^8.0.2"}, "require-dev": {"nunomaduro/larastan": "^2.4", "orchestra/testbench": "^8", "yajra/laravel-datatables-html": "^9.3.4|^10"}, "suggest": {"yajra/laravel-datatables-buttons": "Plugin for server-side exporting of dataTables.", "yajra/laravel-datatables-editor": "Plugin to use DataTables Editor (requires a license).", "yajra/laravel-datatables-export": "Plugin for server-side exporting using livewire and queue worker.", "yajra/laravel-datatables-fractal": "Plugin for server-side response using Fractal.", "yajra/laravel-datatables-html": "Plugin for server-side HTML builder of dataTables."}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}, "laravel": {"providers": ["Yajra\\DataTables\\DataTablesServiceProvider"], "aliases": {"DataTables": "Yajra\\DataTables\\Facades\\DataTables"}}}, "autoload": {"files": ["src/helper.php"], "psr-4": {"Yajra\\DataTables\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "jQuery DataTables API for Laravel 4|5|6|7|8|9|10", "keywords": ["datatables", "j<PERSON>y", "laravel"], "support": {"issues": "https://github.com/yajra/laravel-datatables/issues", "source": "https://github.com/yajra/laravel-datatables/tree/v10.10.0"}, "funding": [{"url": "https://github.com/sponsors/yajra", "type": "github"}], "time": "2023-10-04T02:26:02+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-debugbar", "version": "v3.9.2", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-debugbar.git", "reference": "bfd0131c146973cab164e50f5cdd8a67cc60cab1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-debugbar/zipball/bfd0131c146973cab164e50f5cdd8a67cc60cab1", "reference": "bfd0131c146973cab164e50f5cdd8a67cc60cab1", "shasum": ""}, "require": {"illuminate/routing": "^9|^10", "illuminate/session": "^9|^10", "illuminate/support": "^9|^10", "maximebf/debugbar": "^1.18.2", "php": "^8.0", "symfony/finder": "^6"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench-dusk": "^5|^6|^7|^8", "phpunit/phpunit": "^8.5.30|^9.0", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.8-dev"}, "laravel": {"providers": ["Barryvdh\\Debugbar\\ServiceProvider"], "aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facades\\Debugbar"}}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Barryvdh\\Debugbar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "PHP Debugbar integration for Laravel", "keywords": ["debug", "debugbar", "laravel", "profiler", "webprofiler"], "support": {"issues": "https://github.com/barryvdh/laravel-debugbar/issues", "source": "https://github.com/barryvdh/laravel-debugbar/tree/v3.9.2"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-08-25T18:43:57+00:00"}, {"name": "beyondcode/laravel-dump-server", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/beyondcode/laravel-dump-server.git", "reference": "1f2452617afc64e47b3cf49978beb7beeef084df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beyondcode/laravel-dump-server/zipball/1f2452617afc64e47b3cf49978beb7beeef084df", "reference": "1f2452617afc64e47b3cf49978beb7beeef084df", "shasum": ""}, "require": {"illuminate/console": "5.6.*|5.7.*|5.8.*|^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/http": "5.6.*|5.7.*|5.8.*|^6.0|^7.0|^8.0|^9.0|^10.0", "illuminate/support": "5.6.*|5.7.*|5.8.*|^6.0|^7.0|^8.0|^9.0|^10.0", "php": ">=7.2.5", "symfony/var-dumper": "^5.0|^6.0"}, "require-dev": {"larapack/dd": "^1.0", "phpunit/phpunit": "^7.0|^9.3"}, "type": "library", "extra": {"laravel": {"providers": ["BeyondCode\\DumpServer\\DumpServerServiceProvider"]}}, "autoload": {"files": ["helpers.php"], "psr-4": {"BeyondCode\\DumpServer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://beyondco.de", "role": "Developer"}], "description": "Symfony Var-Dump Server for Laravel", "homepage": "https://github.com/beyondcode/laravel-dump-server", "keywords": ["beyondcode", "laravel-dump-server"], "support": {"issues": "https://github.com/beyondcode/laravel-dump-server/issues", "source": "https://github.com/beyondcode/laravel-dump-server/tree/1.9.0"}, "time": "2023-02-15T10:29:26+00:00"}, {"name": "doctrine/instantiator", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/0a0fa9780f5d4e507415a065172d26a98d02047b", "reference": "0a0fa9780f5d4e507415a065172d26a98d02047b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.30 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.5.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:15:36+00:00"}, {"name": "filp/whoops", "version": "2.15.3", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "c83e88a30524f9360b11f585f71e6b17313b7187"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/c83e88a30524f9360b11f585f71e6b17313b7187", "reference": "c83e88a30524f9360b11f585f71e6b17313b7187", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.15.3"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2023-07-13T12:00:00+00:00"}, {"name": "fzaninotto/faker", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "d0190b156bcca848d401fb80f31f504f37141c8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/d0190b156bcca848d401fb80f31f504f37141c8d", "reference": "d0190b156bcca848d401fb80f31f504f37141c8d", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "suggest": {"ext-intl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/master"}, "abandoned": true, "time": "2015-05-29T06:29:14+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "maximebf/debugbar", "version": "v1.19.1", "source": {"type": "git", "url": "https://github.com/maximebf/php-debugbar.git", "reference": "03dd40a1826f4d585ef93ef83afa2a9874a00523"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maximebf/php-debugbar/zipball/03dd40a1826f4d585ef93ef83afa2a9874a00523", "reference": "03dd40a1826f4d585ef93ef83afa2a9874a00523", "shasum": ""}, "require": {"php": "^7.1|^8", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4|^5|^6"}, "require-dev": {"phpunit/phpunit": ">=7.5.20 <10.0", "twig/twig": "^1.38|^2.7|^3.0"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.18-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/maximebf/php-debugbar", "keywords": ["debug", "debugbar"], "support": {"issues": "https://github.com/maximebf/php-debugbar/issues", "source": "https://github.com/maximebf/php-debugbar/tree/v1.19.1"}, "time": "2023-10-12T08:10:52+00:00"}, {"name": "mockery/mockery", "version": "1.6.6", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "b8e0bb7d8c604046539c1115994632c74dcb361e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/b8e0bb7d8c604046539c1115994632c74dcb361e", "reference": "b8e0bb7d8c604046539c1115994632c74dcb361e", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.10", "psalm/plugin-phpunit": "^0.18.4", "symplify/easy-coding-standard": "^11.5.0", "vimeo/psalm": "^4.30"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2023-08-09T00:03:52+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "reference": "7284c22080590fb39f2ffa3e9057f10a4ddd0e0c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2023-03-08T13:26:56+00:00"}, {"name": "nunomaduro/collision", "version": "v6.4.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "f05978827b9343cba381ca05b8c7deee346b6015"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/f05978827b9343cba381ca05b8c7deee346b6015", "reference": "f05978827b9343cba381ca05b8c7deee346b6015", "shasum": ""}, "require": {"filp/whoops": "^2.14.5", "php": "^8.0.0", "symfony/console": "^6.0.2"}, "require-dev": {"brianium/paratest": "^6.4.1", "laravel/framework": "^9.26.1", "laravel/pint": "^1.1.1", "nunomaduro/larastan": "^1.0.3", "nunomaduro/mock-final-classes": "^1.1.0", "orchestra/testbench": "^7.7", "phpunit/phpunit": "^9.5.23", "spatie/ignition": "^1.4.1"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "6.x-dev"}, "laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2023-01-03T12:54:54+00:00"}, {"name": "phar-io/manifest", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "97803eca37d319dfa7826cc2437fc020857acb53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/97803eca37d319dfa7826cc2437fc020857acb53", "reference": "97803eca37d319dfa7826cc2437fc020857acb53", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.3"}, "time": "2021-07-20T11:28:43+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.29", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "6a3a87ac2bbe33b25042753df8195ba4aa534c76"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-code-coverage/zipball/6a3a87ac2bbe33b25042753df8195ba4aa534c76", "reference": "6a3a87ac2bbe33b25042753df8195ba4aa534c76", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.15", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.3", "phpunit/php-text-template": "^2.0.2", "sebastian/code-unit-reverse-lookup": "^2.0.2", "sebastian/complexity": "^2.0", "sebastian/environment": "^5.1.2", "sebastian/lines-of-code": "^1.0.3", "sebastian/version": "^3.0.1", "theseer/tokenizer": "^1.2.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.29"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-09-19T04:57:46+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.6.13", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "f3d767f7f9e191eab4189abe41ab37797e30b1be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/f3d767f7f9e191eab4189abe41ab37797e30b1be", "reference": "f3d767f7f9e191eab4189abe41ab37797e30b1be", "shasum": ""}, "require": {"doctrine/instantiator": "^1.3.1 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.10.1", "phar-io/manifest": "^2.0.3", "phar-io/version": "^3.0.2", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.28", "phpunit/php-file-iterator": "^3.0.5", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.3", "phpunit/php-timer": "^5.0.2", "sebastian/cli-parser": "^1.0.1", "sebastian/code-unit": "^1.0.6", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.3", "sebastian/environment": "^5.1.3", "sebastian/exporter": "^4.0.5", "sebastian/global-state": "^5.0.1", "sebastian/object-enumerator": "^4.0.3", "sebastian/resource-operations": "^3.0.3", "sebastian/type": "^3.2", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.13"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2023-09-19T05:39:22+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/442e7c7e687e42adc03470c7b668bc4b2402c0b2", "reference": "442e7c7e687e42adc03470c7b668bc4b2402c0b2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:08:49+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/739b35e53379900cc9ac327b2147867b8b6efd88", "reference": "739b35e53379900cc9ac327b2147867b8b6efd88", "shasum": ""}, "require": {"nikic/php-parser": "^4.7", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T15:52:27+00:00"}, {"name": "sebastian/diff", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "74be17022044ebaaecfdf0c5cd504fc9cd5a7131"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/74be17022044ebaaecfdf0c5cd504fc9cd5a7131", "reference": "74be17022044ebaaecfdf0c5cd504fc9cd5a7131", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-05-07T05:35:17+00:00"}, {"name": "sebastian/environment", "version": "5.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d", "reference": "ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T06:03:37+00:00"}, {"name": "sebastian/global-state", "version": "5.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bde739e7565280bda77be70044ac1047bc007e34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bde739e7565280bda77be70044ac1047bc007e34", "reference": "bde739e7565280bda77be70044ac1047bc007e34", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-08-02T09:26:13+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/c1c2e997aa3146983ed888ad08b15470a2e22ecc", "reference": "c1c2e997aa3146983ed888ad08b15470a2e22ecc", "shasum": ""}, "require": {"nikic/php-parser": "^4.6", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-28T06:42:11+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "reference": "0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:45:17+00:00"}, {"name": "sebastian/type", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/34a41e998c2183e22995f158c581e7b5e755ab9e", "reference": "34a41e998c2183e22995f158c581e7b5e755ab9e", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.1"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2021-07-28T10:34:58+00:00"}], "aliases": [], "minimum-stability": "dev", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.0", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}